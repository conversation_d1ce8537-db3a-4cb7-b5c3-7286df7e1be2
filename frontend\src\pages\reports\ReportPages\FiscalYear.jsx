import React, { useEffect, useRef } from "react";
import ApexCharts from "apexcharts";

const FiscalYearDashboard = ({
  headerTextStyle = {},
  headingTextStyle = {},
  subHeadingTextStyle = {},
  contentTextStyle = {},
  fiscalData = null,
  contentSettings = null, // Add contentSettings prop
}) => {
  const stackedColumnRef = useRef(null);
  const netIncomeRef = useRef(null);
  const grossProfitRef = useRef(null);
  const netProfitMarginRef = useRef(null);

  // Enhanced data validation function
  const isDataLoaded = () => {
    if (!fiscalData) return false;

    // Check if required data arrays exist and have content
    const hasMonthlyData = fiscalData.monthlyPerformanceBreakDown &&
      Array.isArray(fiscalData.monthlyPerformanceBreakDown) &&
      fiscalData.monthlyPerformanceBreakDown.length > 0;

    const hasGrossProfitMargin = fiscalData.monthlyGrossProfitMargin &&
      Array.isArray(fiscalData.monthlyGrossProfitMargin) &&
      fiscalData.monthlyGrossProfitMargin.length > 0;

    const hasNetProfitMargin = fiscalData.nerProfitMargin &&
      Array.isArray(fiscalData.nerProfitMargin) &&
      fiscalData.nerProfitMargin.length > 0;

    const hasNetIncomeData = fiscalData.netIncomeLoss &&
      Array.isArray(fiscalData.netIncomeLoss) &&
      fiscalData.netIncomeLoss.length > 0;

    return hasMonthlyData && hasGrossProfitMargin && hasNetProfitMargin && hasNetIncomeData;
  };

  // Function to check if a chart should be displayed based on content settings
  const shouldDisplayChart = (chartKey) => {
    if (!contentSettings?.chartSettings) return true; // Default to true if no settings
    return contentSettings.chartSettings[chartKey] === true;
  };

  useEffect(() => {
    if (isDataLoaded()) {
      // Clear charts first
      [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach((ref) => {
        if (ref.current) {
          ref.current.innerHTML = "";
        }
      });
      // Initialize charts with new data
      initializeCharts();
    }
  }, [fiscalData, contentSettings]); // Add contentSettings to dependency array

  const formatMonthYear = (year, month) => {
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;
  };

  function formatNumber(num) {
    // Round to 2 decimal places to fix floating point precision issues
    const roundedNum = Math.round(num * 100) / 100;
    const isNegative = roundedNum < 0;
    const absNum = Math.abs(roundedNum);

    // For numbers under 10k, show with appropriate decimal places (no suffix)
    if (absNum < 1000) {
      return (isNegative ? "-" : "") + (absNum % 1 === 0 ? absNum.toString() : absNum.toFixed(2));
    }

    const suffixes = [
      { value: 1e12, suffix: "T" },
      { value: 1e9, suffix: "B" },
      { value: 1e6, suffix: "M" },
      { value: 1e3, suffix: "K" },
    ];

    for (let i = 0; i < suffixes.length; i++) {
      if (absNum >= suffixes[i].value) {
        const formatted = (absNum / suffixes[i].value).toFixed(1);
        const cleanFormatted = formatted.endsWith(".0")
          ? formatted.slice(0, -2)
          : formatted;
        return (isNegative ? "-" : "") + cleanFormatted + suffixes[i].suffix;
      }
    }

    return (isNegative ? "-" : "") + roundedNum.toString();
  }

  const initializeCharts = () => {
    if (!fiscalData?.monthlyPerformanceBreakDown) return;

    const monthlyData = fiscalData.monthlyPerformanceBreakDown;

    // Create categories from monthlyPerformanceBreakDown
    const categories = monthlyData.map((item) =>
      formatMonthYear(item.year, item.month)
    );

    // Create lookup maps
    const netProfitMarginMap = new Map();
    if (fiscalData.nerProfitMargin) {
      fiscalData.nerProfitMargin.forEach(item => {
        const key = `${item.year}-${item.month}`;
        const value = parseFloat(item.nerProfitMargin) || 0;
        netProfitMarginMap.set(key, value);
      });
    }

    const grossProfitMarginMap = new Map();
    if (fiscalData.monthlyGrossProfitMargin) {
      fiscalData.monthlyGrossProfitMargin.forEach(item => {
        const key = `${item.year}-${item.month}`;
        const value = parseFloat(item.Gross_Profit_Margin);
        const percentage = value > 1 ? value : value * 100;
        grossProfitMarginMap.set(key, percentage || 0);
      });
    }

    const netIncomeMap = new Map();
    if (fiscalData.netIncomeLoss) {
      fiscalData.netIncomeLoss.forEach(item => {
        const key = `${item.year}-${item.month}`;
        const value = parseFloat(item.netIncomeLoss) / 1000 || 0;
        netIncomeMap.set(key, value);
      });
    }

    // Create aligned data arrays
    const incomeData = monthlyData.map((item) => parseFloat(item.totalIncome) / 1000 || 0);
    const cogsData = monthlyData.map((item) => parseFloat(item.totalCOGS) / 1000 || 0);
    const expenseData = monthlyData.map((item) => parseFloat(item.totalExpenses) / 1000 || 0);

    const grossProfitMarginData = monthlyData.map((item) => {
      const key = `${item.year}-${item.month}`;
      return grossProfitMarginMap.get(key) || 0;
    });

    const netProfitMarginRaw = monthlyData.map((item) => {
      const key = `${item.year}-${item.month}`;
      return netProfitMarginMap.get(key) || 0;
    });

    const netIncomeData = monthlyData.map((item) => {
      const key = `${item.year}-${item.month}`;
      return netIncomeMap.get(key) || 0;
    });

    // 1. Stacked Column Chart (incomeSummary)
    const stackedColumnOptions = {
      series: [
        { name: "Income", type: "line", data: incomeData },
        { name: "Cost of Goods Sold", type: "column", data: cogsData },
        { name: "Expense", type: "column", data: expenseData },
      ],
      chart: {
        height: 450,
        type: "line",
        stacked: true,
        toolbar: { show: false },
        background: "transparent",
      },
      dataLabels: {
        enabled: true,
        enabledOnSeries: [0], // Only show labels on Income line
        formatter: function (val, opts) {
          if (opts.seriesIndex === 0) {
            if (val === null || val === undefined || isNaN(val)) return "";
            const originalValue = val * 1000;
            if (originalValue === 0) return "$0";
            if (originalValue < 1000) {
              return "$" + originalValue.toFixed(0);
            } else if (originalValue < 10000) {
              return "$" + originalValue.toFixed(0);
            } else {
              return "$" + (originalValue / 1000).toFixed(1) + "k";
            }
          }
          return "";
        },
        style: {
          fontSize: "14px",
          colors: ["#20b2aa"],
          fontWeight: "500",
        },
        offsetY: -10,
        background: {
          enabled: false,
        },
        dropShadow: {
          enabled: false,
        },
      },
      stroke: {
        width: [2, 0, 0],
        curve: "smooth",
      },
      plotOptions: {
        bar: {
          columnWidth: "60%", // Wider columns for better COGS visibility
          dataLabels: {
            total: {
              enabled: true,
              offsetY: -20,
              style: {
                fontSize: "12px",
                fontWeight: "500",
                color: "#333",
              },
              formatter: function (val) {
                if (val === null || val === undefined || isNaN(val)) return "$0";
                const originalValue = val * 1000;
                if (originalValue === 0) return "$0";
                if (originalValue < 1000) {
                  return "$" + originalValue.toFixed(0);
                } else if (originalValue < 10000) {
                  return "$" + originalValue.toFixed(0);
                } else {
                  return "$" + (originalValue / 1000).toFixed(1) + "k";
                }
              },
            },
          },
        },
      },
      fill: {
        opacity: [0.8, 1, 0.9], // Make COGS fully opaque, others slightly transparent
      },
      labels: categories,
      markers: {
        size: [5, 0, 0],
        fontSize: "14px",
        strokeColors: "#fff",
        strokeWidth: 2,
        fillOpacity: 1,
        hover: {
          size: 7,
        },
      },
      xaxis: {
        labels: {
          style: {
            colors: "#666",
            fontSize: "12px",
          },
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      yaxis: {
        show: false,
        // More conservative scaling to ensure small COGS values are visible
        min: 0,
        max: function() {
          // Calculate max considering stacked values
          const maxStacked = Math.max(...cogsData.map((cogs, i) => cogs + expenseData[i]));
          const maxIncome = Math.max(...incomeData);
          return Math.max(maxStacked, maxIncome) * 1.2;
        },
      },
      colors: ["#20b2aa", "#4361ee", "#ff8a80"], // Bright blue for COGS contrast
      legend: {
        position: "bottom",
        horizontalAlign: "center",
        fontSize: "14px",
        fontWeight: "400",
        markers: {
          width: 12,
          height: 12,
          radius: 6, // Circular markers
        },
        labels: {
          colors: "#333",
          useSeriesColors: false,
        },
        itemMargin: {
          horizontal: 15,
          vertical: 4,
        },
        offsetY: 10,
        onItemClick: {
          toggleDataSeries: true, // Enable clicking to hide/show series
        },
        onItemHover: {
          highlightDataSeries: true, // Enable hover highlighting
        },
      },
      tooltip: {
        shared: true,
        intersect: false,
        // Custom tooltip to clearly show all values
        custom: function({ series, seriesIndex, dataPointIndex, w }) {
          const income = (series[0][dataPointIndex] * 1000).toFixed(0);
          const cogs = (series[1][dataPointIndex] * 1000).toFixed(0);
          const expense = (series[2][dataPointIndex] * 1000).toFixed(0);
          const category = w.globals.labels[dataPointIndex];
          
          return `
            <div style="padding: 12px; background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); font-size: 14px;">
              <div style="font-weight: 600; margin-bottom: 8px; color: #333;">${category}</div>
              <div style="display: flex; align-items: center; margin-bottom: 4px;">
                <div style="width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-right: 8px;"></div>
                <span style="color: #333;">Income: <strong>$${income}</strong></span>
              </div>
              <div style="display: flex; align-items: center; margin-bottom: 4px; ${cogs == 0 ? 'opacity: 0.6;' : ''}">
                <div style="width: 12px; height: 12px; background: #4361ee; border-radius: 50%; margin-right: 8px;"></div>
                <span style="color: #333;">COGS: <strong>$${cogs}</strong> ${cogs == 0 ? '(No data)' : ''}</span>
              </div>
              <div style="display: flex; align-items: center;">
                <div style="width: 12px; height: 12px; background: #ff8a80; border-radius: 50%; margin-right: 8px;"></div>
                <span style="color: #333;">Expense: <strong>$${expense}</strong></span>
              </div>
              <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
                Total Costs: <strong>$${(parseFloat(cogs) + parseFloat(expense)).toFixed(0)}</strong>
              </div>
            </div>
          `;
        },
      },
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0,
        },
      },
      // Add annotations to highlight if COGS data exists
      annotations: {
        yaxis: cogsData.some(val => val > 0) ? [] : [{
          y: 0,
          borderColor: '#FF4560',
          label: {
            borderColor: '#FF4560',
            style: {
              color: '#fff',
              background: '#FF4560',
            },
            text: 'No COGS data available'
          }
        }]
      }
    };

    // 2. Net Income Chart
    const netIncomeOptions = {
      series: [{
        name: 'Net Income',
        data: netIncomeData
      }],
      chart: {
        type: 'line',
        height: 300,
        toolbar: { show: false },
        background: 'transparent'
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          if (val >= 1) {
            return '$' + val.toFixed(1) + 'k';
          } else if (val >= 0.1) {
            return '$' + val.toFixed(2) + 'k';
          } else if (val <= -0.1) {
            return '-$' + Math.abs(val).toFixed(2) + 'k';
          } else {
            return '$' + val.toFixed(3) + 'k';
          }
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -15,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        curve: 'straight',
        width: 3
      },
      fill: {
        type: 'solid'
      },
      markers: {
        size: 5,
        strokeColors: '#fff',
        strokeWidth: 2,
        colors: netIncomeData.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),
        hover: {
          size: 7
        },
        discrete: netIncomeData.map((val, index) => ({
          seriesIndex: 0,
          dataPointIndex: index,
          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',
          strokeColor: '#fff',
          size: 5
        }))
      },
      xaxis: {
        categories: categories,
        labels: {
          style: {
            colors: '#666',
            fontSize: '14px',
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: ['#1E7C8C'], // Default color for positive values
      plotOptions: {
        line: {
          colors: {
            threshold: 0,
            colorAboveThreshold: '#1E7C8C', // Teal for positive values
            colorBelowThreshold: '#d70015'   // Red for negative values
          }
        }
      },
      tooltip: {
        y: {
          formatter: function (val) {
            if (val >= 1) {
              return '$' + val.toFixed(1) + ' thousand';
            } else if (val >= 0.1) {
              return '$' + val.toFixed(2) + ' thousand';
            } else if (val <= -0.1) {
              return '-$' + Math.abs(val).toFixed(2) + ' thousand';
            } else {
              return '$' + val.toFixed(3) + ' thousand';
            }
          }
        }
      },
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 25,
          bottom: 0
        }
      },
      annotations: {
        yaxis: [{
          y: 0,
          borderColor: '#666',
          borderWidth: 1,
          strokeDashArray: 0,
          opacity: 0.8
        }]
      }
    };

    // 3. Gross Profit Margin Chart
    const grossProfitOptions = {
      series: [{ name: "Gross Profit Margin", data: grossProfitMarginData }],
      chart: {
        type: "bar",
        height: 350,
        toolbar: { show: false },
        background: "transparent",
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: "55%",
          endingShape: "rounded",
          dataLabels: { position: "top" },
        },
      },
      dataLabels: {
        enabled: true,
        position: 'top',
        formatter: function (val) {
          if (val === null || val === undefined || isNaN(val)) return "";
          return val.toFixed(1) + "%";
        },
        offsetY: -20,
        style: { fontSize: "14px", colors: ["#333"], fontWeight: "500" },
      },
      stroke: { show: true, width: 2, colors: ["transparent"] },
      xaxis: {
        categories: categories,
        labels: { style: { colors: "#666", fontSize: "14px" } },
        axisBorder: { show: false },
        axisTicks: { show: false },
      },
      yaxis: {
        show: false,
        min: Math.min(...grossProfitMarginData) < 0 ? Math.min(...grossProfitMarginData) * 1.1 : 0,
        max: Math.max(...grossProfitMarginData) * 1.2,
      },
      fill: { opacity: 1 },
      colors: ["#4a4a9a"],
      tooltip: {
        y: {
          formatter: function (val) {
            if (val === null || val === undefined || isNaN(val)) return "N/A";
            return val.toFixed(1) + "%";
          },
        },
      },
      grid: {
        show: false,
        padding: { left: 25, right: 25, top: 20, bottom: 0 },
      },
    };

    // 4. Net Profit Margin Chart
    const netProfitMarginOptions = {
      series: [{
        name: 'Net Profit Margin',
        data: netProfitMarginRaw
      }],
      chart: {
        type: 'line',
        height: 300,
        toolbar: { show: false },
        background: 'transparent'
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          if (val >= 1) {
            return val.toFixed(1) + '%';
          } else if (val >= 0.1) {
            return val.toFixed(2) + '%';
          } else if (val <= -0.1) {
            return '-' + Math.abs(val).toFixed(2) + '%';
          } else {
            return val.toFixed(3) + '%';
          }
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -15,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        curve: 'straight',
        width: 3
      },
      fill: {
        type: 'solid'
      },
      markers: {
        size: 5,
        strokeColors: '#fff',
        strokeWidth: 2,
        colors: netProfitMarginRaw.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),
        hover: {
          size: 7
        },
        discrete: netProfitMarginRaw.map((val, index) => ({
          seriesIndex: 0,
          dataPointIndex: index,
          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',
          strokeColor: '#fff',
          size: 5
        }))
      },
      xaxis: {
        categories: categories,
        labels: {
          style: {
            colors: '#666',
            fontSize: '14px'
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: ['#1E7C8C'], // Default line color
      plotOptions: {
        line: {
          colors: {
            threshold: 0,
            colorAboveThreshold: '#1E7C8C',
            colorBelowThreshold: '#d70015',
          },
        }
      },
      tooltip: {
        y: {
          formatter: function (val) {
            if (val >= 1) {
              return val.toFixed(1) + '%';
            } else if (val >= 0.1) {
              return val.toFixed(2) + '%';
            } else if (val <= -0.1) {
              return '-' + Math.abs(val).toFixed(2) + '%';
            } else {
              return val.toFixed(3) + '%';
            }
          }
        }
      },
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 25,
          bottom: 0
        }
      },
      annotations: {
        yaxis: [{
          y: 0,
          borderColor: '#666',
          borderWidth: 1,
          strokeDashArray: 0,
          opacity: 0.8
        }]
      }
    };

    // Clear existing charts
    [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach((ref) => {
      if (ref.current) {
        ref.current.innerHTML = "";
      }
    });

    // Render charts conditionally based on content settings
    const chartsToRender = [];

    if (shouldDisplayChart('incomeSummary')) {
      chartsToRender.push({ ref: stackedColumnRef, options: stackedColumnOptions });
    }

    if (shouldDisplayChart('netIncome')) {
      chartsToRender.push({ ref: netIncomeRef, options: netIncomeOptions });
    }

    if (shouldDisplayChart('grossProfitMargin')) {
      chartsToRender.push({ ref: grossProfitRef, options: grossProfitOptions });
    }

    if (shouldDisplayChart('netProfitMargin')) {
      chartsToRender.push({ ref: netProfitMarginRef, options: netProfitMarginOptions });
    }

    // Render the enabled charts
    chartsToRender.forEach(({ ref, options }) => {
      if (ref.current) {
        const chart = new ApexCharts(ref.current, options);
        chart.render();
      }
    });
  };

  // Enhanced loading component with better styling
  const LoadingComponent = () => (
    <div className="min-h-screen p-5">
      <div className="max-w-6xl mx-auto bg-white flex flex-col shadow gap-1 p-10 mb-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            {/* Loading spinner */}
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4"></div>
            <div className="text-xl text-gray-600 mb-2">
              Loading fiscal year data...
            </div>
            <div className="text-sm text-gray-500">
              Please wait while we fetch your financial information
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Show loading if data is not properly loaded
  if (!isDataLoaded()) {
    return <LoadingComponent />;
  }

  // Calculate YTD totals from monthly data
  const calculateYTDTotals = () => {
    if (!fiscalData.monthlyPerformanceBreakDown) return {};

    return fiscalData.monthlyPerformanceBreakDown.reduce(
      (totals, month) => {
        totals.totalIncome += parseFloat(month.totalIncome || 0);
        totals.totalCOGS += parseFloat(month.totalCOGS || 0);
        totals.totalExpenses += parseFloat(month.totalExpenses || 0);
        return totals;
      },
      { totalIncome: 0, totalCOGS: 0, totalExpenses: 0 }
    );
  };

  const ytdTotals = calculateYTDTotals();
  // Fix floating point precision for net profit calculation
  const netProfit =
    Math.round(
      (ytdTotals.totalIncome - ytdTotals.totalCOGS - ytdTotals.totalExpenses) *
      100
    ) / 100;

  return (
    <div className="min-h-screen p-5">
      <div className="max-w-6xl mx-auto bg-white flex flex-col  gap-1 p-10 mb-8">
        {/* Header Section */}
        <div className="component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2">
          <h1
            className="text-4xl font-bold text-gray-800 m-0"
            style={headerTextStyle}
          >
            Current Fiscal Year
          </h1>
          <p className="text-lg text-gray-600 m-0" style={subHeadingTextStyle}>
            January 2025 | Acme Print
          </p>
        </div>

        {/* YTD Fiscal Metrics Grid */}
        <div className="grid grid-cols-4 gap-5 pb-8 border-b-4 border-blue-900">
          <div className="p-4 text-center">
            <div className="text-xl mb-1">YTD Total Income</div>
            <div className="text-4xl font-bold text-gray-600 m-0">
              {formatNumber(ytdTotals.totalIncome)}
            </div>
          </div>
          <div
            className="p-4 text-center"
            style={{ backgroundColor: "#d2e9ea" }}
          >
            <div className="text-xl mb-1">YTD Cost of Goods Sold</div>
            <div className="text-4xl font-bold text-gray-600 m-0">
              {formatNumber(ytdTotals.totalCOGS)}
            </div>
          </div>
          <div className="p-4 text-center">
            <div className="text-xl mb-1">YTD Total Expense</div>
            <div className="text-4xl font-bold text-gray-600 m-0">
              {formatNumber(ytdTotals.totalExpenses)}
            </div>
          </div>
          <div
            className="p-4 text-center"
            style={{ backgroundColor: "#d2e9ea" }}
          >
            <div className="text-xl mb-1">YTD Net Profit</div>
            <div className="text-4xl font-bold text-gray-600 m-0">
              {formatNumber(netProfit)}
            </div>
          </div>
        </div>

        {/* Conditionally render Monthly Performance Breakdown Chart (incomeSummary) */}
        {shouldDisplayChart('incomeSummary') && (
          <div className="bg-white p-6 border-b-4 border-blue-900">
            <div
              className="text-2xl font-semibold text-teal-600 mb-5"
              style={subHeadingTextStyle}
            >
              Monthly Performance Breakdown
            </div>
            <div ref={stackedColumnRef}></div>
          </div>
        )}

        {/* Conditionally render Net Income Chart */}
        {shouldDisplayChart('netIncome') && (
          <div className="bg-white p-6 border-b-4 border-blue-900">
            <div
              className="text-2xl font-semibold text-teal-600 mb-5"
              style={subHeadingTextStyle}
            >
              Net Income/(Loss)
            </div>
            <div ref={netIncomeRef}></div>
          </div>
        )}

        {/* Conditionally render Gross Profit Margin Chart */}
        {shouldDisplayChart('grossProfitMargin') && (
          <div className="bg-white p-6 border-b-4 border-blue-900">
            <div
              className="text-2xl font-semibold text-teal-600 mb-5"
              style={subHeadingTextStyle}
            >
              Gross Profit Margin
            </div>
            <div ref={grossProfitRef}></div>
            <div className="mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5">
              <div
                className="text-teal-600 text-2xl"
                style={{ ...subHeadingTextStyle, fontWeight: "lighter" }}
              >
                Gross Profit Margin
              </div>
              <div style={contentTextStyle}>
                Is a share of Gross Profit in Total Income or the profit left for
                covering operating and other expenses. A good Gross Profit Margin
                is high enough to cover overhead and leave a reasonable Net
                Profit.
              </div>
            </div>
          </div>
        )}

        {/* Conditionally render Net Profit Margin Chart */}
        {shouldDisplayChart('netProfitMargin') && (
          <div className="bg-white p-6 border-b-4 border-blue-900">
            <div
              className="text-2xl font-semibold text-teal-600 mb-5"
              style={subHeadingTextStyle}
            >
              Net Profit Margin
            </div>
            <div ref={netProfitMarginRef}></div>
            <div className="mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5">
              <div
                className="text-teal-600 text-2xl"
                style={{ ...subHeadingTextStyle, fontWeight: "lighter" }}
              >
                Net Profit Margin
              </div>
              <div style={contentTextStyle}>
                Shows the profit earned per dollar of income. A 10% Net Profit
                Margin is considered an excellent ratio. If your company has a low
                Net Profit Margin you are making very little profit after all
                costs. That implies the revenue is getting eaten up by expenses.
                It also increases the risk your firm will be unable to meet
                obligations. With a low margin, a sudden dip in sales over the
                next month or year could turn your company unprofitable. A high
                margin indicates your company has solid competitive advantages.
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FiscalYearDashboard;