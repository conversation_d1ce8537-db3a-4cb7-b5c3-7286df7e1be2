import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();
import { flattenFinancialData } from '../utils/parseData.js';

// Financial report queries
// export const reportQueries = {
//   ytdTotalIncome: (realmId) => `
//     SELECT COALESCE(SUM(pl."amount"), 0) AS "YTD_Total_Income"
//     FROM "ProfitLossReport" pl
//     JOIN "Account" a
//     ON pl."accountId" = a."accountId"
//     AND pl."realmId" = a."realmId"
//     WHERE a."type" = 'Income'
//     AND pl."realmId" = '${realmId}'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 4)
//       OR
//       (pl."year" = 2025 AND pl."month" <= 3)
//     );
//   `,

//   ytdCOGS: (realmId) => `
//     SELECT COALESCE(SUM(pl."amount"), 0) AS "YTD_Total_COGS"
//     FROM "ProfitLossReport" pl
//     JOIN "Account" a
//     ON pl."accountId" = a."accountId"
//     AND pl."realmId" = a."realmId"
//     WHERE a."type" = 'Cost of Goods Sold'
//     AND pl."realmId" = '${realmId}'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 4)
//       OR
//       (pl."year" = 2025 AND pl."month" <= 3)
//     );
//   `,

//   ytdTotalExpense: (realmId) => `
//     SELECT COALESCE(SUM(pl."amount"), 0) AS "YTD_Total_Expense"
//     FROM "ProfitLossReport" pl
//     JOIN "Account" a
//     ON pl."accountId" = a."accountId"
//     AND pl."realmId" = a."realmId"
//     WHERE a."type" = 'Expense'
//     AND pl."realmId" = '${realmId}'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 4)
//       OR
//       (pl."year" = 2025 AND pl."month" <= 3)
//     );
//   `,

//   ytdNetProfit: (realmId) => `
//     SELECT
//       COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" ELSE 0 END), 0) -
//       COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" ELSE 0 END), 0) -
//       COALESCE(SUM(CASE WHEN a."type" = 'Expense' THEN pl."amount" ELSE 0 END), 0) AS "YTD_Net_Profit"
//     FROM "ProfitLossReport" pl
//     JOIN "Account" a
//     ON pl."accountId" = a."accountId"
//     AND pl."realmId" = a."realmId"
//     WHERE a."type" IN ('Income', 'Cost of Goods Sold', 'Expense')
//     AND pl."realmId" = '${realmId}'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 4)
//       OR
//       (pl."year" = 2025 AND pl."month" <= 3)
//     );
//   `,

//   netIncomeMonthly: (realmId) => `
//     SELECT
//       pl."year",
//       pl."month",
//       COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" ELSE 0 END), 0) AS "Income",
//       COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" ELSE 0 END), 0) AS "COGS",
//       COALESCE(SUM(CASE WHEN a."type" = 'Expense' THEN pl."amount" ELSE 0 END), 0) AS "Expense"
//     FROM "ProfitLossReport" pl
//     JOIN "Account" a
//     ON pl."accountId" = a."accountId"
//     WHERE pl."realmId" = '${realmId}'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 1) OR
//       (pl."year" = 2025 AND pl."month" <= 1)
//     )
//     GROUP BY pl."year", pl."month"
//     ORDER BY pl."year", pl."month";
//   `,

//   grossProfitMargin: (realmId) => `
//     WITH months AS (
//       SELECT 2024 AS year, generate_series(4, 12) AS month
//       UNION ALL
//       SELECT 2025 AS year, generate_series(1, 3)
//     ),
//     income AS (
//       SELECT
//         "year",
//         "month",
//         SUM("amount") AS total_income
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Income'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//     ),
//     cogs AS (
//       SELECT
//         "year",
//         "month",
//         SUM("amount") AS total_cogs
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Cost of Goods Sold'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//     )
//     SELECT
//       m.year,
//       m.month,
//       COALESCE(i.total_income, 0) AS total_income,
//       COALESCE(c.total_cogs, 0) AS total_cogs,
//       COALESCE(i.total_income, 0) - COALESCE(c.total_cogs, 0) AS gross_profit,
//       ROUND(
//         CASE
//           WHEN COALESCE(i.total_income, 0) = 0 THEN 0
//           ELSE ((COALESCE(i.total_income, 0) - COALESCE(c.total_cogs, 0)) / COALESCE(i.total_income, 1)) * 100
//         END, 2
//       ) AS gross_profit_margin_percentage
//     FROM months m
//     LEFT JOIN income i ON m.year = i.year AND m.month = i.month
//     LEFT JOIN cogs c ON m.year = c.year AND m.month = c.month
//     ORDER BY m.year, m.month;
//   `,

//   netProfitMargin: (realmId) => `
//     WITH months AS (
//       SELECT 2024 AS year, generate_series(4, 12) AS month
//       UNION ALL
//       SELECT 2025 AS year, generate_series(1, 3)
//     ),
//     income AS (
//       SELECT
//         "year",
//         "month",
//         SUM("amount") AS total_income
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Income'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//       ORDER BY "year", "month"
//     ),
//     cogs AS (
//       SELECT
//         "year",
//         "month",
//         SUM("amount") AS total_cogs
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Cost of Goods Sold'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//       ORDER BY "year", "month"
//     ),
//     expenses AS (
//       SELECT
//         "year",
//         "month",
//         SUM("amount") AS total_expenses
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Expense'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//       ORDER BY "year", "month"
//     )
//     SELECT
//       m.year,
//       m.month,
//       COALESCE(i.total_income, 0) AS total_income,
//       COALESCE(c.total_cogs, 0) AS total_cogs,
//       COALESCE(e.total_expenses, 0) AS total_expenses,
//       COALESCE(i.total_income, 0) - COALESCE(c.total_cogs, 0) - COALESCE(e.total_expenses, 0) AS net_profit,
//       ROUND(
//         CASE
//           WHEN COALESCE(i.total_income, 0) = 0 THEN 0
//           ELSE ((COALESCE(i.total_income, 0) - COALESCE(c.total_cogs, 0) - COALESCE(e.total_expenses, 0)) / COALESCE(i.total_income, 1)) * 100
//         END, 2
//       ) AS net_profit_margin_percentage
//     FROM months m
//     LEFT JOIN income i ON m.year = i.year AND m.month = i.month
//     LEFT JOIN cogs c ON m.year = c.year AND m.month = c.month
//     LEFT JOIN expenses e ON m.year = e.year AND m.month = e.month
//     ORDER BY m.year, m.month;
//   `,

//   expensesTopAccountsMonthly : (realmId) => `
//     WITH expense_data AS (
//   SELECT
//     a."name" AS account_name,
//     pr."year",
//     pr."month",
//     SUM(pr."amount") AS monthly_expense
//   FROM
//     "ProfitLossReport" pr
//   JOIN
//     "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
//   WHERE
//     pr."realmId" = '${realmId}'
//     AND a."type" = 'Expense'
//     AND (
//       (pr."year" = 2024 AND pr."month" >= 4) OR
//       (pr."year" = 2025 AND pr."month" <= 3)
//     )
//   GROUP BY
//     a."name", pr."year", pr."month"
// ),
// total_by_account AS (
//   SELECT
//     account_name,
//     SUM(monthly_expense) AS total_expense
//   FROM expense_data
//   GROUP BY account_name
// ),
// top_accounts AS (
//   SELECT account_name
//   FROM total_by_account
//   ORDER BY total_expense DESC
//   LIMIT 10
// ),
// classified_expenses AS (
//   SELECT
//     CASE
//       WHEN ed.account_name IN (SELECT account_name FROM top_accounts)
//       THEN ed.account_name
//       ELSE 'Other'
//     END AS account_name,
//     ed.year,
//     ed.month,
//     SUM(ed.monthly_expense) AS monthly_expense
//   FROM expense_data ed
//   GROUP BY
//     CASE
//       WHEN ed.account_name IN (SELECT account_name FROM top_accounts)
//       THEN ed.account_name
//       ELSE 'Other'
//     END,
//     ed.year, ed.month
// ),
// pivoted AS (
//   SELECT
//     account_name,
//     SUM(CASE WHEN year = 2024 AND month = 4 THEN monthly_expense ELSE 0 END) AS apr_24,
//     SUM(CASE WHEN year = 2024 AND month = 5 THEN monthly_expense ELSE 0 END) AS may_24,
//     SUM(CASE WHEN year = 2024 AND month = 6 THEN monthly_expense ELSE 0 END) AS jun_24,
//     SUM(CASE WHEN year = 2024 AND month = 7 THEN monthly_expense ELSE 0 END) AS jul_24,
//     SUM(CASE WHEN year = 2024 AND month = 8 THEN monthly_expense ELSE 0 END) AS aug_24,
//     SUM(CASE WHEN year = 2024 AND month = 9 THEN monthly_expense ELSE 0 END) AS sep_24,
//     SUM(CASE WHEN year = 2024 AND month = 10 THEN monthly_expense ELSE 0 END) AS oct_24,
//     SUM(CASE WHEN year = 2024 AND month = 11 THEN monthly_expense ELSE 0 END) AS nov_24,
//     SUM(CASE WHEN year = 2024 AND month = 12 THEN monthly_expense ELSE 0 END) AS dec_24,
//     SUM(CASE WHEN year = 2025 AND month = 1 THEN monthly_expense ELSE 0 END) AS jan_25,
//     SUM(CASE WHEN year = 2025 AND month = 2 THEN monthly_expense ELSE 0 END) AS feb_25,
//     SUM(CASE WHEN year = 2025 AND month = 3 THEN monthly_expense ELSE 0 END) AS mar_25,
//     SUM(monthly_expense) AS total_expense
//   FROM classified_expenses
//   GROUP BY account_name
// )
// SELECT *
// FROM pivoted
// ORDER BY total_expense DESC;

//   `,

//   expensesTopAccounts: (realmId) => `
//     WITH expense_data AS (
//       SELECT
//         a."name" AS account_name,
//         SUM(pr."amount") AS total_expense
//       FROM
//         "ProfitLossReport" pr
//       JOIN
//         "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
//       WHERE
//         pr."realmId" = '${realmId}'
//         AND a."type" = 'Expense'
//         AND (
//           (pr."year" = 2024 AND pr."month" >= 4) OR
//           (pr."year" = 2025 AND pr."month" <= 3)
//         )
//       GROUP BY
//         a."name"
//     ),
//     top_accounts AS (
//       SELECT *
//       FROM expense_data
//       ORDER BY total_expense DESC
//       LIMIT 10
//     ),
//     other_total AS (
//       SELECT
//         'Other' AS account_name,
//         SUM(ed.total_expense) AS total_expense
//       FROM expense_data ed
//       WHERE ed.account_name NOT IN (SELECT account_name FROM top_accounts)
//     ),
//     combined AS (
//       SELECT * FROM top_accounts
//       UNION ALL
//       SELECT * FROM other_total
//     ),
//     total_sum AS (
//       SELECT SUM(total_expense) AS grand_total FROM combined
//     )
//     SELECT
//       c.account_name,
//       c.total_expense,
//       ROUND((c.total_expense / t.grand_total) * 100, 2) AS percentage_of_total
//     FROM
//       combined c,
//       total_sum t
//     ORDER BY
//       c.total_expense DESC;
//   `,

//   daysSalesAROutstanding: (realmId) => `
//     WITH months AS (
//       SELECT 2024 AS year, generate_series(4, 12) AS month
//       UNION ALL
//       SELECT 2025 AS year, generate_series(1, 3)
//     ),
//     income AS (
//       SELECT "year", "month", SUM("amount") AS total_income
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Income'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//     ),
//     ar AS (
//       SELECT "year", "month", SUM("total") AS accounts_receivable
//       FROM "AccountReceivableAgingSummaryReport"
//       WHERE "realmId" = '${realmId}'
//       GROUP BY "year", "month"
//     ),
//     days_in_month AS (
//       SELECT year, month,
//              EXTRACT(DAY FROM (DATE_TRUNC('month', TO_DATE(year || '-' || month || '-01', 'YYYY-MM-DD'))
//              + INTERVAL '1 month - 1 day'))::int AS days
//       FROM months
//     )
//     SELECT
//       m.year,
//       m.month,
//       COALESCE(ar.accounts_receivable, 0) AS accounts_receivable,
//       COALESCE(i.total_income, 0) AS total_income,
//       d.days AS days_in_month,
//       ROUND(
//         CASE
//           WHEN COALESCE(i.total_income, 0) = 0 THEN 0
//           ELSE COALESCE(ar.accounts_receivable, 0) / (i.total_income / d.days)
//         END, 2
//       ) AS days_sales_outstanding
//     FROM months m
//     LEFT JOIN income i ON m.year = i.year AND m.month = i.month
//     LEFT JOIN ar ON m.year = ar.year AND m.month = ar.month
//     LEFT JOIN days_in_month d ON m.year = d.year AND m.month = d.month
//     ORDER BY m.year, m.month;
//   `,

//   daysSalesAPOutstanding: (realmId) => `
//     WITH months AS (
//       SELECT 2024 AS year, generate_series(4, 12) AS month
//       UNION ALL
//       SELECT 2025 AS year, generate_series(1, 3)
//     ),
//     income AS (
//       SELECT "year", "month", SUM("amount") AS total_income
//       FROM "ProfitLossReport" pl
//       JOIN (
//         SELECT DISTINCT "accountId"
//         FROM "Account"
//         WHERE "type" = 'Income'
//           AND "realmId" = '${realmId}'
//       ) a ON pl."accountId" = a."accountId"
//       WHERE pl."realmId" = '${realmId}'
//       GROUP BY "year", "month"
//     ),
//     ap AS (
//       SELECT "year", "month", SUM("total") AS accounts_payable
//       FROM "AccountPayableAgingSummaryReport"
//       WHERE "realmId" = '${realmId}'
//       GROUP BY "year", "month"
//     ),
//     days_in_month AS (
//       SELECT year, month,
//              EXTRACT(DAY FROM (DATE_TRUNC('month', TO_DATE(year || '-' || month || '-01', 'YYYY-MM-DD'))
//              + INTERVAL '1 month - 1 day'))::int AS days
//       FROM months
//     )
//     SELECT
//       m.year,
//       m.month,
//       COALESCE(ap.accounts_payable, 0) AS accounts_payable,
//       COALESCE(i.total_income, 0) AS total_income,
//       d.days AS days_in_month,
//       ROUND(
//         CASE
//           WHEN COALESCE(i.total_income, 0) = 0 THEN 0
//           ELSE COALESCE(ap.accounts_payable, 0) / (i.total_income / d.days)
//         END, 2
//       ) AS days_payables_outstanding
//     FROM months m
//     LEFT JOIN income i ON m.year = i.year AND m.month = i.month
//     LEFT JOIN ap ON m.year = ap.year AND m.month = ap.month
//     LEFT JOIN days_in_month d ON m.year = d.year AND m.month = d.month
//     ORDER BY m.year, m.month;
//   `,

//   daysInventoryOutstanding: (realmId) => `
//     WITH months AS (
//       SELECT 2024 AS year, generate_series(4, 12) AS month
//       UNION ALL
//       SELECT 2025 AS year, generate_series(1, 3) AS month
//     ),
//     inventory AS (
//       SELECT
//         b."year",
//         b."month",
//         MAX(b."statementAmount") AS inventory_balance
//       FROM "BalanceSheetReport" b
//       JOIN "Account" a ON a."accountId" = b."accountId"
//       WHERE b."realmId" = '${realmId}'
//         AND a."accountClassification" = 'Asset'
//         AND (
//           a."name" ILIKE '%inventory%' OR
//           a."accountSubTypeName" ILIKE '%inventory%'
//         )
//       GROUP BY b."year", b."month"
//     ),
//     cogs AS (
//       SELECT
//         "year",
//         "month",
//         SUM("amount") AS total_cogs
//       FROM (
//         SELECT DISTINCT ON (p."year", p."month", p."accountId")
//           p."year",
//           p."month",
//           p."accountId",
//           p."amount"
//         FROM "ProfitLossReport" p
//         JOIN "Account" a ON a."accountId" = p."accountId"
//         WHERE p."realmId" = '${realmId}'
//           AND a."type" = 'Cost of Goods Sold'
//       ) deduped
//       GROUP BY "year", "month"
//     ),
//     days_in_month AS (
//       SELECT
//         m.year,
//         m.month,
//         EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(m.year, m.month, 1))
//                           + INTERVAL '1 month - 1 day'))::int AS days
//       FROM months m
//     ),
//     avg_inventory AS (
//       SELECT
//         curr.year,
//         curr.month,
//         (COALESCE(prev.inventory_balance, curr.inventory_balance) + curr.inventory_balance) / 2.0 AS average_inventory
//       FROM inventory curr
//       LEFT JOIN inventory prev
//         ON (prev.year = curr.year AND prev.month = curr.month - 1)
//         OR (prev.year = curr.year - 1 AND prev.month = 12 AND curr.month = 1)
//     )
//     SELECT
//       m.year,
//       m.month,
//       ai.average_inventory,
//       c.total_cogs,
//       d.days,
//       ROUND(
//         CASE
//           WHEN c.total_cogs IS NULL OR c.total_cogs = 0 THEN 0
//           ELSE ai.average_inventory / (c.total_cogs / d.days)
//         END, 2
//       ) AS days_inventory_outstanding
//     FROM months m
//     LEFT JOIN avg_inventory ai ON ai.year = m.year AND ai.month = m.month
//     LEFT JOIN cogs c ON c.year = m.year AND c.month = m.month
//     LEFT JOIN days_in_month d ON d.year = m.year AND d.month = m.month
//     ORDER BY year, month;
//   `,

//   netChangeInCash: (realmId) => `
//     WITH month_series AS (
//       SELECT EXTRACT(YEAR FROM d)::INT AS "year",
//              EXTRACT(MONTH FROM d)::INT AS "month"
//       FROM generate_series(
//         DATE '2024-04-01',
//         DATE '2025-04-01',
//         INTERVAL '1 month'
//       ) d
//     ),
//     monthly_cash AS (
//       SELECT
//         b."year",
//         b."month",
//         SUM(b."statementAmount") AS closing_balance
//       FROM "BalanceSheetReport" b
//       JOIN "Account" a ON b."accountId" = a."accountId" AND b."realmId" = a."realmId"
//       WHERE a."type" IN ('Bank', 'Cash')
//         AND b."realmId" = '${realmId}'
//       GROUP BY b."year", b."month"
//     ),
//     cash_change AS (
//       SELECT
//         m."year",
//         m."month",
//         mc.closing_balance,
//         LAG(mc.closing_balance) OVER (ORDER BY m."year", m."month") AS opening_balance
//       FROM month_series m
//       LEFT JOIN monthly_cash mc ON m."year" = mc."year" AND m."month" = mc."month"
//     )
//     SELECT
//       "year",
//       "month",
//       CASE
//         WHEN closing_balance IS NULL OR opening_balance IS NULL THEN '0.00'
//         ELSE (closing_balance - opening_balance)::TEXT
//       END AS net_change_in_cash
//     FROM cash_change
//     ORDER BY "year", "month";
//   `,

//   quickRatio: (realmId) => `
//     WITH month_series AS (
//   SELECT
//     EXTRACT(YEAR FROM d)::INT AS "year",
//     EXTRACT(MONTH FROM d)::INT AS "month"
//   FROM generate_series(
//     DATE '2024-04-01',
//     DATE '2025-04-01',
//     INTERVAL '1 month'
//   ) d
// ),
// balances AS (
//   SELECT
//     b."year",
//     b."month",
//     b."statementAmount" AS amount,
//     a."type",
//     a."accountSubTypeName"
//   FROM "BalanceSheetReport" b
//   JOIN "Account" a ON b."accountId" = a."accountId" AND b."realmId" = a."realmId"
//   WHERE b."realmId" = '${realmId}'
// ),
// monthly_aggregates AS (
//   SELECT
//     "year",
//     "month",
//     SUM(CASE WHEN "type" = 'Bank' THEN amount ELSE 0 END) AS cash,
//     SUM(CASE WHEN "type" = 'Accounts Receivable' THEN amount ELSE 0 END) AS accounts_receivable,
//     SUM(CASE WHEN "accountSubTypeName" IN ('CreditCard', 'AccountsPayable', 'OtherCurrentLiabilities') THEN amount ELSE 0 END) AS current_liabilities
//   FROM balances
//   GROUP BY "year", "month"
// ),
// full_view AS (
//   SELECT
//     m."year",
//     m."month",
//     COALESCE(ma.cash, 0) AS cash,
//     COALESCE(ma.accounts_receivable, 0) AS accounts_receivable,
//     COALESCE(ma.current_liabilities, 0) AS current_liabilities
//   FROM month_series m
//   LEFT JOIN monthly_aggregates ma ON m."year" = ma."year" AND m."month" = ma."month"
// )
// SELECT
//   "year",
//   "month",
//   cash,
//   accounts_receivable,
//   current_liabilities,
//   CASE
//     WHEN current_liabilities = 0 THEN '0.00'
//     ELSE ROUND((cash + accounts_receivable) / current_liabilities, 2)::TEXT
//   END AS quick_ratio
// FROM full_view
// ORDER BY "year", "month";

//   `,

//   roeRoa : (realmId) => `
//   WITH pnl AS (
//   SELECT
//     pl."year",
//     pl."month",
//     COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" ELSE 0 END), 0) AS income,
//     COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" ELSE 0 END), 0) AS cogs,
//     COALESCE(SUM(CASE WHEN a."type" IN ('Expense', 'Other Expense') THEN pl."amount" ELSE 0 END), 0) AS expense,
//     COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" ELSE 0 END), 0) -
//     COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" ELSE 0 END), 0) -
//     COALESCE(SUM(CASE WHEN a."type" IN ('Expense', 'Other Expense') THEN pl."amount" ELSE 0 END), 0) AS net_profit
//   FROM "ProfitLossReport" pl
//   JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId"
//   WHERE pl."realmId" = '${realmId}'
//     AND (
//       ("year" = 2024 AND "month" >= 4) OR
//       ("year" = 2025 AND "month" <= 3)
//     )
//   GROUP BY pl."year", pl."month"
// ),
// bs AS (
//   SELECT
//     bs."year",
//     bs."month",
//     COALESCE(SUM(CASE WHEN a."type" = 'Asset' THEN bs."statementAmount" ELSE 0 END), 0) AS total_assets,
//     COALESCE(SUM(CASE WHEN a."type" = 'Equity' THEN bs."statementAmount" ELSE 0 END), 0) AS total_equity
//   FROM "BalanceSheetReport" bs
//   JOIN "Account" a ON bs."accountId" = a."accountId" AND bs."realmId" = a."realmId"
//   WHERE bs."realmId" = '${realmId}'
//     AND (
//       (bs."year" = 2024 AND bs."month" >= 4) OR
//       (bs."year" = 2025 AND bs."month" <= 3)
//     )
//   GROUP BY bs."year", bs."month"
// )
// SELECT
//   pnl."year",
//   pnl."month",
//   pnl.income,
//   pnl.cogs,
//   pnl.expense,
//   pnl.net_profit,
//   bs.total_assets,
//   bs.total_equity,
//   ROUND(COALESCE(CASE WHEN bs.total_assets <> 0 THEN pnl.net_profit / bs.total_assets ELSE 0 END, 0), 2) AS roa,
//   ROUND(COALESCE(CASE WHEN bs.total_equity <> 0 THEN pnl.net_profit / bs.total_equity ELSE 0 END, 0), 2) AS roe
// FROM pnl
// LEFT JOIN bs ON pnl."year" = bs."year" AND pnl."month" = bs."month"
// ORDER BY pnl."year", pnl."month";
//   `,

//   cashConversionCycle : (realmId) => `
//   WITH ar AS (
//   SELECT "year", "month", COALESCE(SUM("total"), 0) AS ar_balance
//   FROM "AccountReceivableAgingSummaryReport"
//   WHERE "realmId" = '${realmId}'
//     AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
//   GROUP BY "year", "month"
// ),
// ap AS (
//   SELECT "year", "month", COALESCE(SUM("total"), 0) AS ap_balance
//   FROM "AccountPayableAgingSummaryReport"
//   WHERE "realmId" = '${realmId}'
//     AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
//   GROUP BY "year", "month"
// ),
// inventory AS (
//   SELECT bs."year", bs."month", COALESCE(SUM(bs."statementAmount"), 0) AS inventory_balance
//   FROM "BalanceSheetReport" bs
//   JOIN "Account" a ON bs."accountId" = a."accountId" AND bs."realmId" = a."realmId" AND bs."userId" = a."userId"
//   WHERE bs."realmId" = '${realmId}'
//     AND a."type" = 'Asset'
//     AND a."name" ILIKE '%inventory%'
//     AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
//   GROUP BY bs."year", bs."month"
// ),
// revenue AS (
//   SELECT pl."year", pl."month", COALESCE(SUM(pl."amount"), 0) AS income
//   FROM "ProfitLossReport" pl
//   JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId" AND pl."userId" = a."userId"
//   WHERE pl."realmId" = '${realmId}'
//     AND a."type" = 'Income'
//     AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
//   GROUP BY pl."year", pl."month"
// ),
// cogs AS (
//   SELECT pl."year", pl."month", COALESCE(SUM(pl."amount"), 0) AS cogs
//   FROM "ProfitLossReport" pl
//   JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId" AND pl."userId" = a."userId"
//   WHERE pl."realmId" = '${realmId}'
//     AND a."type" = 'Cost of Goods Sold'
//     AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
//   GROUP BY pl."year", pl."month"
// )
// SELECT
//   COALESCE(re."year", co."year", ar."year", ap."year", inv."year") AS "year",
//   COALESCE(re."month", co."month", ar."month", ap."month", inv."month") AS "month",
//   COALESCE(re.income, 0) AS income,
//   COALESCE(ar.ar_balance, 0) AS ar_balance,
//   COALESCE(co.cogs, 0) AS cogs,
//   COALESCE(inv.inventory_balance, 0) AS inventory_balance,
//   COALESCE(ap.ap_balance, 0) AS ap_balance,
//   ROUND(CASE WHEN COALESCE(re.income, 0) <> 0 THEN (COALESCE(ar.ar_balance, 0) / re.income) * 30 ELSE 0 END, 2) AS dso,
//   ROUND(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (COALESCE(inv.inventory_balance, 0) / co.cogs) * 30 ELSE 0 END, 2) AS dio,
//   ROUND(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (COALESCE(ap.ap_balance, 0) / co.cogs) * 30 ELSE 0 END, 2) AS dpo,
//   ROUND(
//     COALESCE(CASE WHEN COALESCE(re.income, 0) <> 0 THEN (ar.ar_balance / re.income) * 30 ELSE 0 END, 0) +
//     COALESCE(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (inv.inventory_balance / co.cogs) * 30 ELSE 0 END, 0) -
//     COALESCE(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (ap.ap_balance / co.cogs) * 30 ELSE 0 END, 0),
//     2
//   ) AS ccc
// FROM revenue re
// FULL OUTER JOIN cogs co ON re."year" = co."year" AND re."month" = co."month"
// FULL OUTER JOIN ar ON re."year" = ar."year" AND re."month" = ar."month"
// FULL OUTER JOIN ap ON re."year" = ap."year" AND re."month" = ap."month"
// FULL OUTER JOIN inventory inv ON re."year" = inv."year" AND re."month" = inv."month"
// ORDER BY "year", "month";
//   `,

//   fixedAssetTurnover: (realmId) => `
//   WITH revenue AS (
//   SELECT
//     pl."year",
//     pl."month",
//     COALESCE(SUM(pl."amount"), 0) AS total_revenue
//   FROM "ProfitLossReport" pl
//   JOIN "Account" a ON
//     pl."accountId" = a."accountId" AND
//     pl."realmId" = a."realmId" AND
//     pl."userId" = a."userId"
//   WHERE pl."realmId" = '${realmId}'
//     AND a."type" = 'Income'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 4) OR
//       (pl."year" = 2025 AND pl."month" <= 3)
//     )
//   GROUP BY pl."year", pl."month"
// ),
// fixed_assets AS (
//   SELECT
//     bs."year",
//     bs."month",
//     COALESCE(SUM(bs."statementAmount"), 0) AS net_fixed_assets
//   FROM "BalanceSheetReport" bs
//   JOIN "Account" a ON
//     bs."accountId" = a."accountId" AND
//     bs."realmId" = a."realmId" AND
//     bs."userId" = a."userId"
//   WHERE bs."realmId" = '${realmId}'
//     AND a."type" = 'Asset'
//     AND a."name" ILIKE '%fixed%'
//     AND (
//       (bs."year" = 2024 AND bs."month" >= 3) OR
//       (bs."year" = 2025 AND bs."month" <= 3)
//     )
//   GROUP BY bs."year", bs."month"
// ),
// combined AS (
//   SELECT
//     r."year",
//     r."month",
//     COALESCE(r.total_revenue, 0) AS total_revenue,
//     COALESCE(fa.net_fixed_assets, 0) AS net_fixed_assets,
//     COALESCE(LAG(fa.net_fixed_assets) OVER (ORDER BY r."year", r."month"), 0) AS prev_fixed_assets
//   FROM revenue r
//   LEFT JOIN fixed_assets fa ON r."year" = fa."year" AND r."month" = fa."month"
// )
// SELECT
//   "year",
//   "month",
//   total_revenue,
//   net_fixed_assets,
//   prev_fixed_assets,
//   ROUND(
//     CASE
//       WHEN (net_fixed_assets + prev_fixed_assets) = 0 THEN 0
//       ELSE total_revenue / ((net_fixed_assets + prev_fixed_assets) / 2)
//     END,
//     2
//   ) AS fat
// FROM combined
// ORDER BY "year", "month";
//   `,

//   monthsCashOnHand : (realmId) => `
//   WITH cash_ar AS (
//   SELECT
//     bs."year",
//     bs."month",
//     COALESCE(SUM(CASE
//       WHEN a."name" ILIKE '%cash%' THEN bs."statementAmount"
//       WHEN a."name" ILIKE '%receivable%' THEN bs."statementAmount"
//       ELSE 0
//     END), 0) AS cash_ar_total
//   FROM "BalanceSheetReport" bs
//   JOIN "Account" a ON
//     bs."accountId" = a."accountId" AND
//     bs."realmId" = a."realmId" AND
//     bs."userId" = a."userId"
//   WHERE bs."realmId" = '${realmId}'
//     AND (
//       (bs."year" = 2024 AND bs."month" >= 4) OR
//       (bs."year" = 2025 AND bs."month" <= 3)
//     )
//   GROUP BY bs."year", bs."month"
// ),
// total_expense AS (
//   SELECT
//     COALESCE(SUM(pl."amount"), 0) AS yearly_expense
//   FROM "ProfitLossReport" pl
//   JOIN "Account" a ON
//     pl."accountId" = a."accountId" AND
//     pl."realmId" = a."realmId" AND
//     pl."userId" = a."userId"
//   WHERE pl."realmId" = '${realmId}'
//     AND a."type" = 'Expense'
//     AND (
//       (pl."year" = 2024 AND pl."month" >= 4) OR
//       (pl."year" = 2025 AND pl."month" <= 3)
//     )
// ),
// monthly_avg_expense AS (
//   SELECT ROUND(COALESCE(yearly_expense, 0) / 12.0, 2) AS avg_monthly_expense FROM total_expense
// )
// SELECT
//   ca."year",
//   ca."month",
//   ROUND(COALESCE(ca.cash_ar_total, 0), 2) AS cash_ar_total,
//   ROUND(COALESCE(mae.avg_monthly_expense, 0), 2) AS avg_monthly_expense,
//   ROUND(
//     CASE
//       WHEN COALESCE(mae.avg_monthly_expense, 0) = 0 THEN 0
//       ELSE COALESCE(ca.cash_ar_total, 0) / mae.avg_monthly_expense
//     END,
//     2
//   ) AS months_cash
// FROM cash_ar ca
// CROSS JOIN monthly_avg_expense mae
// ORDER BY ca."year", ca."month";
//   `
// };

export const reportQueries = {
  // YTD Summary Query
  ytdSummary: (realmId) => `
  SELECT
    COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" END), 0) AS "YTD_Total_Income",
    COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" END), 0) AS "YTD_Total_COGS",
    COALESCE(SUM(CASE WHEN a."type" = 'Expense' THEN pl."amount" END), 0) AS "YTD_Total_Expense",
    COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" ELSE 0 END), 0)
      - COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" ELSE 0 END), 0)
      - COALESCE(SUM(CASE WHEN a."type" = 'Expense' THEN pl."amount" ELSE 0 END), 0)
      AS "YTD_Net_Profit"
  FROM "ProfitLossReport" pl
  JOIN "Account" a
    ON pl."accountId" = a."accountId"
    AND pl."realmId" = a."realmId"
    AND pl."realmId" = '${realmId}'
    AND a."type" IN ('Income', 'Cost of Goods Sold', 'Expense')
    AND (
      (pl."year" = 2024 AND pl."month" >= 4)
      OR
      (pl."year" = 2025 AND pl."month" <= 3)
    );
`,

  // Monthly P&L with Gross Profit Margin
  monthlyProfitLoss: (realmId) => `
  SELECT
    pl."year",
    pl."month",
    CASE
      WHEN COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" END), 0) = 0 THEN 0
      ELSE ROUND(
        (
          COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" END), 0) -
          COALESCE(SUM(CASE WHEN a."type" = 'Cost of Goods Sold' THEN pl."amount" END), 0)
        )
        /
        COALESCE(SUM(CASE WHEN a."type" = 'Income' THEN pl."amount" END), 1),
        4
      )
    END AS "Gross_Profit_Margin"
  FROM "ProfitLossReport" pl
  JOIN "Account" a
    ON pl."accountId" = a."accountId"
    AND pl."realmId" = a."realmId"
    WHERE pl."realmId" = '${realmId}'
    AND a."type" IN ('Income', 'Cost of Goods Sold', 'Expense')
    AND (
      (pl."year" = 2024 AND pl."month" >= 4)
      OR
      (pl."year" = 2025 AND pl."month" <= 3)
    )
  GROUP BY pl."year", pl."month"
  ORDER BY pl."year", pl."month";
`,

  // Monthly Financial Summary with Net Profit Margin
  monthlyFinancialSummary: (realmId) => `
  WITH months AS (
    SELECT 2024 AS year, generate_series(4, 12) AS month
    UNION ALL
    SELECT 2025 AS year, generate_series(1, 3)
  ),
  income AS (
    SELECT
      "year",
      "month",
      SUM("amount") AS total_income
    FROM "ProfitLossReport" pl
    JOIN (
      SELECT DISTINCT "accountId"
      FROM "Account"
      WHERE "type" = 'Income'
        AND "realmId" = '${realmId}'
    ) a ON pl."accountId" = a."accountId"
    WHERE pl."realmId" = '${realmId}'
    GROUP BY "year", "month"
    ORDER BY "year", "month"
  ),
  cogs AS (
    SELECT
      "year",
      "month",
      SUM("amount") AS total_cogs
    FROM "ProfitLossReport" pl
    JOIN (
      SELECT DISTINCT "accountId"
      FROM "Account"
      WHERE "type" = 'Cost of Goods Sold'
        AND "realmId" = '${realmId}'
    ) a ON pl."accountId" = a."accountId"
    WHERE pl."realmId" = '${realmId}'
    GROUP BY "year", "month"
    ORDER BY "year", "month"
  ),
  expenses AS (
    SELECT
      "year",
      "month",
      SUM("amount") AS total_expenses
    FROM "ProfitLossReport" pl
    JOIN (
      SELECT DISTINCT "accountId"
      FROM "Account"
      WHERE "type" = 'Expense'
        AND "realmId" = '${realmId}'
    ) a ON pl."accountId" = a."accountId"
    WHERE pl."realmId" = '${realmId}'
    GROUP BY "year", "month"
    ORDER BY "year", "month"
  )
  SELECT
    m.year,
    m.month,
    COALESCE(i.total_income, 0) AS total_income,
    COALESCE(c.total_cogs, 0) AS total_cogs,
    COALESCE(e.total_expenses, 0) AS total_expenses,
    COALESCE(i.total_income, 0) - COALESCE(c.total_cogs, 0) - COALESCE(e.total_expenses, 0) AS net_profit,
    ROUND(
      CASE 
        WHEN COALESCE(i.total_income, 0) = 0 THEN 0
        ELSE ((COALESCE(i.total_income, 0) - COALESCE(c.total_cogs, 0) - COALESCE(e.total_expenses, 0)) / COALESCE(i.total_income, 1)) * 100
      END, 2
    ) AS net_profit_margin_percentage
  FROM months m
  LEFT JOIN income i ON m.year = i.year AND m.month = i.month
  LEFT JOIN cogs c ON m.year = c.year AND m.month = c.month
  LEFT JOIN expenses e ON m.year = e.year AND m.month = e.month
  ORDER BY m.year, m.month;
`,

  // Top Expenses Breakdown
  topExpensesBreakdown: (realmId) => `
  WITH expense_data AS (
    SELECT
      a."name" AS account_name,
      SUM(pr."amount") AS total_expense
    FROM "ProfitLossReport" pr
    JOIN "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
    WHERE
      pr."realmId" = '${realmId}'
      AND a."type" = 'Expense'
      AND (
        (pr."year" = 2024 AND pr."month" >= 4) OR
        (pr."year" = 2025 AND pr."month" <= 3)
      )
    GROUP BY a."name"
  ),
  total_sum AS (
    SELECT SUM(total_expense) AS grand_total FROM expense_data
  ),
  top_accounts AS (
    SELECT *
    FROM expense_data
    ORDER BY total_expense DESC
    LIMIT 10
  ),
  other_total AS (
    SELECT
      'Other' AS account_name,
      SUM(ed.total_expense) AS total_expense
    FROM expense_data ed
    WHERE ed.account_name NOT IN (SELECT account_name FROM top_accounts)
  ),
  combined AS (
    SELECT * FROM top_accounts
    UNION ALL
    SELECT * FROM other_total
  )
  SELECT
    c.account_name,
    c.total_expense,
    ROUND((c.total_expense / ts.grand_total) * 100, 2) AS percentage_of_total
  FROM combined c, total_sum ts
  ORDER BY total_expense DESC NULLS LAST;
`,
  topExpensesMonthWise: (realmId) => `WITH expense_data AS (
    SELECT
      a."name" AS account_name,
      SUM(pr."amount") AS total_expense
    FROM "ProfitLossReport" pr
    JOIN "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
    WHERE
      pr."realmId" = '${realmId}'
      AND a."type" = 'Expense'
      AND (
        (pr."year" = 2024 AND pr."month" >= 4) OR
        (pr."year" = 2025 AND pr."month" <= 3)
      )
    GROUP BY a."name"
  ),
  expense_monthly AS (
    SELECT
      a."name" AS account_name,
      pr."year",
      pr."month",
      SUM(pr."amount") AS monthly_expense,
      -- Create a readable month-year format
      CASE 
        WHEN pr."month" = 1 THEN 'Jan ' || pr."year"
        WHEN pr."month" = 2 THEN 'Feb ' || pr."year"
        WHEN pr."month" = 3 THEN 'Mar ' || pr."year"
        WHEN pr."month" = 4 THEN 'Apr ' || pr."year"
        WHEN pr."month" = 5 THEN 'May ' || pr."year"
        WHEN pr."month" = 6 THEN 'Jun ' || pr."year"
        WHEN pr."month" = 7 THEN 'Jul ' || pr."year"
        WHEN pr."month" = 8 THEN 'Aug ' || pr."year"
        WHEN pr."month" = 9 THEN 'Sep ' || pr."year"
        WHEN pr."month" = 10 THEN 'Oct ' || pr."year"
        WHEN pr."month" = 11 THEN 'Nov ' || pr."year"
        WHEN pr."month" = 12 THEN 'Dec ' || pr."year"
      END AS month_year
    FROM "ProfitLossReport" pr
    JOIN "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
    WHERE
      pr."realmId" = '${realmId}'
      AND a."type" = 'Expense'
      AND (
        (pr."year" = 2024 AND pr."month" >= 4) OR
        (pr."year" = 2025 AND pr."month" <= 3)
      )
    GROUP BY a."name", pr."year", pr."month"
  ),
  total_sum AS (
    SELECT SUM(total_expense) AS grand_total FROM expense_data
  ),
  top_accounts AS (
    SELECT *
    FROM expense_data
    ORDER BY total_expense DESC
    LIMIT 10
  ),
  other_total AS (
    SELECT
      'Other' AS account_name,
      SUM(ed.total_expense) AS total_expense
    FROM expense_data ed
    WHERE ed.account_name NOT IN (SELECT account_name FROM top_accounts)
  ),
  other_monthly AS (
    SELECT
      'Other' AS account_name,
      em."year",
      em."month",
      em.month_year,
      SUM(em.monthly_expense) AS monthly_expense
    FROM expense_monthly em
    WHERE em.account_name NOT IN (SELECT account_name FROM top_accounts)
    GROUP BY em."year", em."month", em.month_year
  ),
  combined_totals AS (
    SELECT * FROM top_accounts
    UNION ALL
    SELECT * FROM other_total
  ),
  combined_monthly AS (
    SELECT 
      em.account_name,
      em."year",
      em."month",
      em.month_year,
      em.monthly_expense
    FROM expense_monthly em
    WHERE em.account_name IN (SELECT account_name FROM top_accounts)
    UNION ALL
    SELECT * FROM other_monthly
  )
  
-- Pivot table showing months as columns (alternative format)
SELECT
  cm.account_name,
  ct.total_expense,
  ROUND((ct.total_expense / ts.grand_total) * 100, 2) AS percentage_of_total,
  
  -- Monthly columns (Apr 2024 - Mar 2025)
  SUM(CASE WHEN cm."year" = 2024 AND cm."month" = 4 THEN cm.monthly_expense ELSE 0 END) AS "Apr_2024",
  SUM(CASE WHEN cm."year" = 2024 AND cm."month" = 5 THEN cm.monthly_expense ELSE 0 END) AS "May_2024",
  SUM(CASE WHEN cm."year" = 2024 AND cm."month" = 6 THEN cm.monthly_expense ELSE 0 END) AS "Jun_2024",
  SUM(CASE WHEN cm."year" = 2024 AND cm."month" = 7 THEN cm.monthly_expense ELSE 0 END) AS "Jul_2024",
  SUM(CASE WHEN cm."year" = 2024 AND cm."month" = 8 THEN cm.monthly_expense ELSE 0 END) AS "Aug_2024",
  SUM(CASE WHEN cm."year" = 2024 AND cm."month" = 9 THEN cm.monthly_expense ELSE 0 END) AS "Sep_2024",
  SUM(CASE WHEN cm."year" = 2024 AND cm."month" = 10 THEN cm.monthly_expense ELSE 0 END) AS "Oct_2024",
  SUM(CASE WHEN cm."year" = 2024 AND cm."month" = 11 THEN cm.monthly_expense ELSE 0 END) AS "Nov_2024",
  SUM(CASE WHEN cm."year" = 2024 AND cm."month" = 12 THEN cm.monthly_expense ELSE 0 END) AS "Dec_2024",
  SUM(CASE WHEN cm."year" = 2025 AND cm."month" = 1 THEN cm.monthly_expense ELSE 0 END) AS "Jan_2025",
  SUM(CASE WHEN cm."year" = 2025 AND cm."month" = 2 THEN cm.monthly_expense ELSE 0 END) AS "Feb_2025",
  SUM(CASE WHEN cm."year" = 2025 AND cm."month" = 3 THEN cm.monthly_expense ELSE 0 END) AS "Mar_2025"
  
FROM combined_monthly cm
JOIN combined_totals ct ON cm.account_name = ct.account_name
CROSS JOIN total_sum ts
GROUP BY cm.account_name, ct.total_expense, ts.grand_total
ORDER BY 
  CASE WHEN cm.account_name = 'Other' THEN 1 ELSE 0 END,
  ct.total_expense DESC;`,

  expensesTopAccountsMonthly: (realmId) => `  WITH expense_data AS (
  SELECT
    a."name" AS account_name,
    pr."year",
    pr."month",
    SUM(pr."amount") AS monthly_expense
  FROM
    "ProfitLossReport" pr
  JOIN
    "Account" a ON pr."accountId" = a."accountId" AND pr."realmId" = a."realmId"
  WHERE
    pr."realmId" = '${realmId}'
    AND a."type" = 'Expense'
    AND (
      (pr."year" = 2024 AND pr."month" >= 4) OR
      (pr."year" = 2025 AND pr."month" <= 3)
    )
  GROUP BY
    a."name", pr."year", pr."month"
),
total_by_account AS (
  SELECT
    account_name,
    SUM(monthly_expense) AS total_expense
  FROM expense_data
  GROUP BY account_name
),
top_accounts AS (
  SELECT account_name
  FROM total_by_account
  ORDER BY total_expense DESC
  LIMIT 10
),
classified_expenses AS (
  SELECT
    CASE
      WHEN ed.account_name IN (SELECT account_name FROM top_accounts)
      THEN ed.account_name
      ELSE 'Other'
    END AS account_name,
    ed.year,
    ed.month,
    SUM(ed.monthly_expense) AS monthly_expense
  FROM expense_data ed
  GROUP BY
    CASE
      WHEN ed.account_name IN (SELECT account_name FROM top_accounts)
      THEN ed.account_name
      ELSE 'Other'
    END,
    ed.year, ed.month
),
pivoted AS (
  SELECT
    account_name,
    SUM(CASE WHEN year = 2024 AND month = 4 THEN monthly_expense ELSE 0 END) AS apr_24,
    SUM(CASE WHEN year = 2024 AND month = 5 THEN monthly_expense ELSE 0 END) AS may_24,
    SUM(CASE WHEN year = 2024 AND month = 6 THEN monthly_expense ELSE 0 END) AS jun_24,
    SUM(CASE WHEN year = 2024 AND month = 7 THEN monthly_expense ELSE 0 END) AS jul_24,
    SUM(CASE WHEN year = 2024 AND month = 8 THEN monthly_expense ELSE 0 END) AS aug_24,
    SUM(CASE WHEN year = 2024 AND month = 9 THEN monthly_expense ELSE 0 END) AS sep_24,
    SUM(CASE WHEN year = 2024 AND month = 10 THEN monthly_expense ELSE 0 END) AS oct_24,
    SUM(CASE WHEN year = 2024 AND month = 11 THEN monthly_expense ELSE 0 END) AS nov_24,
    SUM(CASE WHEN year = 2024 AND month = 12 THEN monthly_expense ELSE 0 END) AS dec_24,
    SUM(CASE WHEN year = 2025 AND month = 1 THEN monthly_expense ELSE 0 END) AS jan_25,
    SUM(CASE WHEN year = 2025 AND month = 2 THEN monthly_expense ELSE 0 END) AS feb_25,
    SUM(CASE WHEN year = 2025 AND month = 3 THEN monthly_expense ELSE 0 END) AS mar_25,
    SUM(monthly_expense) AS total_expense
  FROM classified_expenses
  GROUP BY account_name
)
SELECT *
FROM pivoted
ORDER BY total_expense DESC;
  `,

  // Days Sales Outstanding (DSO)
  daysSalesOutstanding: (realmId) => `
  WITH months AS (
    SELECT 2024 AS year, generate_series(4, 12) AS month
    UNION ALL
    SELECT 2025 AS year, generate_series(1, 3)
  ),
  income AS (
    SELECT pl."year", pl."month", SUM(pl."amount") AS total_income
    FROM "ProfitLossReport" pl
    JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId"
    WHERE a."type" = 'Income'
      AND pl."realmId" = '${realmId}'
    GROUP BY pl."year", pl."month"
  ),
  ar AS (
    SELECT "year", "month", SUM("total") AS accounts_receivable
    FROM "AccountReceivableAgingSummaryReport"
    WHERE "realmId" = '${realmId}'
    GROUP BY "year", "month"
  ),
  days_in_month AS (
    SELECT
      m.year,
      m.month,
      EXTRACT(DAY FROM (DATE_TRUNC('month', TO_DATE(m.year || '-' || m.month || '-01', 'YYYY-MM-DD'))
      + INTERVAL '1 month - 1 day'))::int AS days
    FROM months m
  )
  SELECT
    m.year,
    m.month,
    ROUND(
      CASE 
        WHEN COALESCE(i.total_income, 0) = 0 THEN 0
        ELSE COALESCE(ar.accounts_receivable, 0) / (i.total_income / d.days)
      END, 2
    ) AS days_sales_outstanding
  FROM months m
  LEFT JOIN income i ON m.year = i.year AND m.month = i.month
  LEFT JOIN ar ON m.year = ar.year AND m.month = ar.month
  LEFT JOIN days_in_month d ON m.year = d.year AND m.month = d.month
  ORDER BY m.year, m.month;
`,

  // Days Payables Outstanding (DPO)
  daysPayablesOutstanding: (realmId) => `
  WITH months AS (
    SELECT 2024 AS year, generate_series(4, 12) AS month
    UNION ALL
    SELECT 2025 AS year, generate_series(1, 3)
  ),
  income AS (
    SELECT
      pl."year",
      pl."month",
      SUM(pl."amount") AS total_income
    FROM "ProfitLossReport" pl
    JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId"
    WHERE a."type" = 'Income'
      AND pl."realmId" = '${realmId}'
    GROUP BY pl."year", pl."month"
  ),
  ap AS (
    SELECT "year", "month", SUM("total") AS accounts_payable
    FROM "AccountPayableAgingSummaryReport"
    WHERE "realmId" = '${realmId}'
    GROUP BY "year", "month"
  ),
  days_in_month AS (
    SELECT
      m.year,
      m.month,
      EXTRACT(DAY FROM (DATE_TRUNC('month', TO_DATE(m.year || '-' || m.month || '-01', 'YYYY-MM-DD'))
      + INTERVAL '1 month - 1 day'))::int AS days
    FROM months m
  )
  SELECT
    m.year,
    m.month,
    ROUND(
      CASE 
        WHEN COALESCE(i.total_income, 0) = 0 THEN 0
        ELSE COALESCE(ap.accounts_payable, 0) / (i.total_income / d.days)
      END, 2
    ) AS days_payables_outstanding
  FROM months m
  LEFT JOIN income i ON m.year = i.year AND m.month = i.month
  LEFT JOIN ap ON m.year = ap.year AND m.month = ap.month
  LEFT JOIN days_in_month d ON m.year = d.year AND m.month = d.month
  ORDER BY m.year, m.month;
`,

  // Net Change in Cash
  netChangeInCash: (realmId) => `
  WITH month_series AS (
    SELECT EXTRACT(YEAR FROM d)::INT AS "year",
           EXTRACT(MONTH FROM d)::INT AS "month"
    FROM generate_series(
      DATE '2024-04-01',
      DATE '2025-04-01',
      INTERVAL '1 month'
    ) d
  ),
  monthly_cash AS (
    SELECT
      b."year",
      b."month",
      SUM(b."statementAmount") AS closing_balance
    FROM "BalanceSheetReport" b
    JOIN "Account" a ON b."accountId" = a."accountId" AND b."realmId" = a."realmId"
    WHERE a."type" IN ('Bank', 'Cash')
      AND b."realmId" = '${realmId}'
    GROUP BY b."year", b."month"
  ),
  cash_change AS (
    SELECT
      m."year",
      m."month",
      mc.closing_balance,
      LAG(mc.closing_balance) OVER (ORDER BY m."year", m."month") AS opening_balance
    FROM month_series m
    LEFT JOIN monthly_cash mc ON m."year" = mc."year" AND m."month" = mc."month"
  )
  SELECT
    "year",
    "month",
    CASE
      WHEN closing_balance IS NULL OR opening_balance IS NULL THEN '0.00'
      ELSE (closing_balance - opening_balance)::TEXT
    END AS net_change_in_cash
  FROM cash_change
  ORDER BY "year", "month";
`,

  // Quick Ratio
  quickRatio: (realmId) => `
  WITH month_series AS (
    SELECT
      EXTRACT(YEAR FROM d)::INT AS "year",
      EXTRACT(MONTH FROM d)::INT AS "month"
    FROM generate_series(
      DATE '2024-04-01',
      DATE '2025-03-31',
      INTERVAL '1 month'
    ) d
  ),
  balances AS (
    SELECT
      b."year",
      b."month",
      b."statementAmount" AS amount,
      a."type",
      a."accountSubTypeName"
    FROM "BalanceSheetReport" b
    JOIN "Account" a ON b."accountId" = a."accountId" AND b."realmId" = a."realmId"
    WHERE b."realmId" = '${realmId}'
  ),
  monthly_aggregates AS (
    SELECT
      "year",
      "month",
      SUM(CASE WHEN "type" = 'Bank' THEN amount ELSE 0 END) AS cash,
      SUM(CASE WHEN "type" = 'Accounts Receivable' THEN amount ELSE 0 END) AS accounts_receivable,
      SUM(CASE WHEN "accountSubTypeName" IN ('CreditCard', 'AccountsPayable', 'OtherCurrentLiabilities') THEN amount ELSE 0 END) AS current_liabilities
    FROM balances
    GROUP BY "year", "month"
  ),
  full_view AS (
    SELECT
      m."year",
      m."month",
      COALESCE(ma.cash, 0) AS cash,
      COALESCE(ma.accounts_receivable, 0) AS accounts_receivable,
      COALESCE(ma.current_liabilities, 0) AS current_liabilities
    FROM month_series m
    LEFT JOIN monthly_aggregates ma ON m."year" = ma."year" AND m."month" = ma."month"
  )
  SELECT
    "year",
    "month",
    CASE
      WHEN current_liabilities = 0 THEN '0.00'
      ELSE ROUND((cash + accounts_receivable) / current_liabilities, 2)::TEXT
    END AS quick_ratio
  FROM full_view
  ORDER BY "year", "month";
`,

  // ROA and ROE
  roaAndRoe: (realmId) => `
WITH pnl AS (
  SELECT
    pl."year",
    pl."month",
    SUM(
      CASE
        WHEN a."type" = 'Income' THEN pl."amount"
        WHEN a."type" = 'Cost of Goods Sold' THEN -pl."amount"
        WHEN a."type" IN ('Expense', 'Other Expense') THEN -pl."amount"
        ELSE 0
      END
    ) AS net_profit
  FROM "ProfitLossReport" pl
  JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId"
  WHERE pl."realmId" = '${realmId}'
    AND (
      (pl."year" = 2024 AND pl."month" >= 4) OR
      (pl."year" = 2025 AND pl."month" <= 3)
    )
  GROUP BY pl."year", pl."month"
),
bs AS (
  SELECT
    bs."year",
    bs."month",
    SUM(CASE WHEN a."accountClassification" = 'Asset' THEN bs."statementAmount" ELSE 0 END) AS total_assets,
    SUM(CASE WHEN a."accountClassification" = 'Equity' THEN bs."statementAmount" ELSE 0 END) AS total_equity
  FROM "BalanceSheetReport" bs
  JOIN "Account" a ON bs."accountId" = a."accountId" AND bs."realmId" = a."realmId"
  WHERE bs."realmId" = '${realmId}'
    AND (
      (bs."year" = 2024 AND bs."month" >= 4) OR
      (bs."year" = 2025 AND bs."month" <= 3)
    )
  GROUP BY bs."year", bs."month"
)
SELECT
  pnl."year",
  pnl."month",
  ROUND(CASE WHEN bs.total_assets <> 0 THEN pnl.net_profit * 100.0 / bs.total_assets ELSE NULL END, 2) AS roa,
  ROUND(CASE WHEN bs.total_equity <> 0 THEN pnl.net_profit * 100.0 / bs.total_equity ELSE NULL END, 2) AS roe
FROM pnl
LEFT JOIN bs ON pnl."year" = bs."year" AND pnl."month" = bs."month"
WHERE
  (bs.total_assets IS NOT NULL AND bs.total_assets <> 0)
  OR (bs.total_equity IS NOT NULL AND bs.total_equity <> 0)
ORDER BY pnl."year", pnl."month";
`,

  // Cash Conversion Cycle (CCC)
  cashConversionCycle: (realmId) => `
  WITH ar AS (
    SELECT "year", "month", COALESCE(SUM("total"), 0) AS ar_balance
    FROM "AccountReceivableAgingSummaryReport"
    WHERE "realmId" = '${realmId}'
      AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
    GROUP BY "year", "month"
  ),
  ap AS (
    SELECT "year", "month", COALESCE(SUM("total"), 0) AS ap_balance
    FROM "AccountPayableAgingSummaryReport"
    WHERE "realmId" = '${realmId}'
      AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
    GROUP BY "year", "month"
  ),
  inventory AS (
    SELECT bs."year", bs."month", COALESCE(SUM(bs."statementAmount"), 0) AS inventory_balance
    FROM "BalanceSheetReport" bs
    JOIN "Account" a ON bs."accountId" = a."accountId" AND bs."realmId" = a."realmId" AND bs."userId" = a."userId"
    WHERE bs."realmId" = '${realmId}'
      AND a."type" = 'Asset'
      AND a."name" ILIKE '%inventory%'
      AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
    GROUP BY bs."year", bs."month"
  ),
  revenue AS (
    SELECT pl."year", pl."month", COALESCE(SUM(pl."amount"), 0) AS income
    FROM "ProfitLossReport" pl
    JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId" AND pl."userId" = a."userId"
    WHERE pl."realmId" = '${realmId}'
      AND a."type" = 'Income'
      AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
    GROUP BY pl."year", pl."month"
  ),
  cogs AS (
    SELECT pl."year", pl."month", COALESCE(SUM(pl."amount"), 0) AS cogs
    FROM "ProfitLossReport" pl
    JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId" AND pl."userId" = a."userId"
    WHERE pl."realmId" = '${realmId}'
      AND a."type" = 'Cost of Goods Sold'
      AND (("year" = 2024 AND "month" >= 4) OR ("year" = 2025 AND "month" <= 3))
    GROUP BY pl."year", pl."month"
  )
  SELECT
    COALESCE(re."year", co."year", ar."year", ap."year", inv."year") AS "year",
    COALESCE(re."month", co."month", ar."month", ap."month", inv."month") AS "month",
    COALESCE(re.income, 0) AS income,
    COALESCE(ar.ar_balance, 0) AS ar_balance,
    COALESCE(co.cogs, 0) AS cogs,
    COALESCE(inv.inventory_balance, 0) AS inventory_balance,
    COALESCE(ap.ap_balance, 0) AS ap_balance,
    ROUND(CASE WHEN COALESCE(re.income, 0) <> 0 THEN (COALESCE(ar.ar_balance, 0) / re.income) * 30 ELSE 0 END, 2) AS dso,
    ROUND(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (COALESCE(inv.inventory_balance, 0) / co.cogs) * 30 ELSE 0 END, 2) AS dio,
    ROUND(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (COALESCE(ap.ap_balance, 0) / co.cogs) * 30 ELSE 0 END, 2) AS dpo,
    ROUND(
      COALESCE(CASE WHEN COALESCE(re.income, 0) <> 0 THEN (ar.ar_balance / re.income) * 30 ELSE 0 END, 0) +
      COALESCE(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (inv.inventory_balance / co.cogs) * 30 ELSE 0 END, 0) -
      COALESCE(CASE WHEN COALESCE(co.cogs, 0) <> 0 THEN (ap.ap_balance / co.cogs) * 30 ELSE 0 END, 0),
      2
    ) AS ccc
  FROM revenue re
  FULL OUTER JOIN cogs co ON re."year" = co."year" AND re."month" = co."month"
  FULL OUTER JOIN ar ON re."year" = ar."year" AND re."month" = ar."month"
  FULL OUTER JOIN ap ON re."year" = ap."year" AND re."month" = ap."month"
  FULL OUTER JOIN inventory inv ON re."year" = inv."year" AND re."month" = inv."month"
  ORDER BY "year", "month";
`,

  // Fixed Asset Turnover (FAT)
  fixedAssetTurnover: (realmId) => `
  WITH revenue AS (
    SELECT
      pl."year",
      pl."month",
      COALESCE(SUM(pl."amount"), 0) AS total_revenue
    FROM "ProfitLossReport" pl
    JOIN "Account" a ON
      pl."accountId" = a."accountId" AND
      pl."realmId" = a."realmId" AND
      pl."userId" = a."userId"
    WHERE pl."realmId" = '${realmId}'
      AND a."type" = 'Income'
      AND (
        (pl."year" = 2024 AND pl."month" >= 4) OR
        (pl."year" = 2025 AND pl."month" <= 3)
      )
    GROUP BY pl."year", pl."month"
  ),
  fixed_assets AS (
    SELECT
      bs."year",
      bs."month",
      COALESCE(SUM(bs."statementAmount"), 0) AS net_fixed_assets
    FROM "BalanceSheetReport" bs
    JOIN "Account" a ON
      bs."accountId" = a."accountId" AND
      bs."realmId" = a."realmId" AND
      bs."userId" = a."userId"
    WHERE bs."realmId" = '${realmId}'
      AND a."type" = 'Asset'
      AND a."name" ILIKE '%fixed%'
      AND (
        (bs."year" = 2024 AND bs."month" >= 3) OR
        (bs."year" = 2025 AND bs."month" <= 3)
      )
    GROUP BY bs."year", bs."month"
  ),
  combined AS (
    SELECT
      r."year",
      r."month",
      COALESCE(r.total_revenue, 0) AS total_revenue,
      COALESCE(fa.net_fixed_assets, 0) AS net_fixed_assets,
      COALESCE(LAG(fa.net_fixed_assets) OVER (ORDER BY r."year", r."month"), 0) AS prev_fixed_assets
    FROM revenue r
    LEFT JOIN fixed_assets fa ON r."year" = fa."year" AND r."month" = fa."month"
  )
  SELECT
    "year",
    "month",
    ROUND(
      CASE
        WHEN (net_fixed_assets + prev_fixed_assets) = 0 THEN 0
        ELSE total_revenue / ((net_fixed_assets + prev_fixed_assets) / 2)
      END,
      2
    ) AS fat
  FROM combined
  ORDER BY "year", "month";
`,

  // Months of Cash
  monthsOfCash: (realmId) => `
  WITH months AS (
    SELECT
      EXTRACT(YEAR FROM d)::INT AS "year",
      EXTRACT(MONTH FROM d)::INT AS "month",
      d::DATE AS ref_date,
      EXTRACT(DAY FROM (DATE_TRUNC('month', d) + INTERVAL '1 month - 1 day'))::INT AS days_in_month
    FROM generate_series(
      DATE '2024-04-01',
      DATE '2025-03-01',
      INTERVAL '1 month'
    ) d
  ),
  
  -- Cash + A/R from Balance Sheet
  cash_ar AS (
    SELECT
      bs."year",
      bs."month",
      SUM(CASE WHEN a."type" = 'Bank' THEN bs."statementAmount" ELSE 0 END) AS cash,
      SUM(CASE WHEN a."type" = 'Accounts Receivable' THEN bs."statementAmount" ELSE 0 END) AS ar
    FROM "BalanceSheetReport" bs
    JOIN "Account" a ON bs."accountId" = a."accountId" AND bs."realmId" = a."realmId"
    WHERE bs."realmId" = '${realmId}'
    GROUP BY bs."year", bs."month"
  ),
  
  -- Monthly COGS + Expense from P&L
  monthly_expense AS (
    SELECT
      pl."year",
      pl."month",
      SUM(CASE WHEN a."type" IN ('Expense', 'Cost of Goods Sold') THEN pl."amount" ELSE 0 END) AS monthly_expense
    FROM "ProfitLossReport" pl
    JOIN "Account" a ON pl."accountId" = a."accountId" AND pl."realmId" = a."realmId"
    WHERE pl."realmId" = '${realmId}'
    GROUP BY pl."year", pl."month"
  ),
  
  -- Flatten for rolling average
  pnl_with_date AS (
    SELECT
      MAKE_DATE(me."year", me."month", 1) AS period,
      me.monthly_expense
    FROM monthly_expense me
  ),
  
  -- Trailing 12-month average operating expense
  avg_expense_12mo AS (
    SELECT
      m."year",
      m."month",
      ROUND(AVG(p.monthly_expense)::NUMERIC, 2) AS avg_monthly_operating_expense
    FROM months m
    JOIN pnl_with_date p ON p.period BETWEEN (m.ref_date - INTERVAL '11 months') AND m.ref_date
    GROUP BY m."year", m."month"
  )
  
  -- Final result
  SELECT
    m."year",
    m."month",
    ROUND(COALESCE(ca.cash, 0) + COALESCE(ca.ar, 0), 2) AS cash_plus_ar,
    ROUND(COALESCE(me.monthly_expense, 0), 2) AS monthly_expense,
    ae.avg_monthly_operating_expense,
    m.days_in_month,
    ROUND(
      CASE
        WHEN ae.avg_monthly_operating_expense = 0 THEN 0
        ELSE (COALESCE(ca.cash, 0) + COALESCE(ca.ar, 0)) / ae.avg_monthly_operating_expense
      END,
      2
    ) AS months_cash
  FROM months m
  LEFT JOIN cash_ar ca ON m."year" = ca."year" AND m."month" = ca."month"
  LEFT JOIN monthly_expense me ON m."year" = me."year" AND m."month" = me."month"
  LEFT JOIN avg_expense_12mo ae ON m."year" = ae."year" AND m."month" = ae."month"
  ORDER BY m."year", m."month";
`,

  // YTD Total Income (as requested in your example)
  ytdTotalIncome: (realmId) => `
  SELECT COALESCE(SUM(pl."amount"), 0) AS "YTD_Total_Income"
  FROM "ProfitLossReport" pl
  JOIN "Account" a
  ON pl."accountId" = a."accountId"
  AND pl."realmId" = a."realmId"
  WHERE a."type" = 'Income'
  AND pl."realmId" = '${realmId}'
  AND (
    (pl."year" = 2024 AND pl."month" >= 4)
    OR
    (pl."year" = 2025 AND pl."month" <= 3)
  );
`,

  // 13 month trailing P&L by accounts
  profitAndLossMonthsTrailing: (realmId) => `
  WITH months AS (
    SELECT * FROM (
      VALUES
        (2024, 4, 'Apr 24'), (2024, 5, 'May 24'), (2024, 6, 'Jun 24'),
        (2024, 7, 'Jul 24'), (2024, 8, 'Aug 24'), (2024, 9, 'Sep 24'),
        (2024, 10, 'Oct 24'), (2024, 11, 'Nov 24'), (2024, 12, 'Dec 24'),
        (2025, 1, 'Jan 25'), (2025, 2, 'Feb 25'), (2025, 3, 'Mar 25'), (2025, 4, 'Apr 25')
    ) AS m("year", "month", "label")
  ),
  all_accounts AS (
    SELECT
      "accountId",
      "name" AS account_name,
      "type" AS account_type
    FROM public."Account"
    WHERE "realmId" = '${realmId}'
  ),
  expanded_grid AS (
    SELECT
      aa."accountId",
      aa.account_name,
      aa.account_type,
      m."year",
      m."month",
      m."label"
    FROM all_accounts aa
    CROSS JOIN months m
  ),
  joined_data AS (
    SELECT
      eg.account_type,
      eg.account_name,
      eg."label" AS month_label,
      COALESCE(SUM(pl."amount"), 0) AS amount
    FROM expanded_grid eg
    LEFT JOIN "ProfitLossReport" pl
      ON pl."accountId"::text = eg."accountId"
      AND pl."realmId" = '${realmId}'
      AND pl."year" = eg."year"
      AND pl."month" = eg."month"
    GROUP BY eg.account_type, eg.account_name, eg."label"
  ),
  pivoted AS (
    SELECT
      account_type,
      account_name,
      SUM(CASE WHEN month_label = 'Apr 24' THEN amount ELSE 0 END) AS "Apr 24",
      SUM(CASE WHEN month_label = 'May 24' THEN amount ELSE 0 END) AS "May 24",
      SUM(CASE WHEN month_label = 'Jun 24' THEN amount ELSE 0 END) AS "Jun 24",
      SUM(CASE WHEN month_label = 'Jul 24' THEN amount ELSE 0 END) AS "Jul 24",
      SUM(CASE WHEN month_label = 'Aug 24' THEN amount ELSE 0 END) AS "Aug 24",
      SUM(CASE WHEN month_label = 'Sep 24' THEN amount ELSE 0 END) AS "Sep 24",
      SUM(CASE WHEN month_label = 'Oct 24' THEN amount ELSE 0 END) AS "Oct 24",
      SUM(CASE WHEN month_label = 'Nov 24' THEN amount ELSE 0 END) AS "Nov 24",
      SUM(CASE WHEN month_label = 'Dec 24' THEN amount ELSE 0 END) AS "Dec 24",
      SUM(CASE WHEN month_label = 'Jan 25' THEN amount ELSE 0 END) AS "Jan 25",
      SUM(CASE WHEN month_label = 'Feb 25' THEN amount ELSE 0 END) AS "Feb 25",
      SUM(CASE WHEN month_label = 'Mar 25' THEN amount ELSE 0 END) AS "Mar 25",
      SUM(CASE WHEN month_label = 'Apr 25' THEN amount ELSE 0 END) AS "Apr 25"
    FROM joined_data
    GROUP BY account_type, account_name
  ),
  type_totals AS (
    SELECT
      account_type,
      'Total ' || account_type AS account_name,
      SUM("Apr 24") AS "Apr 24",
      SUM("May 24") AS "May 24",
      SUM("Jun 24") AS "Jun 24",
      SUM("Jul 24") AS "Jul 24",
      SUM("Aug 24") AS "Aug 24",
      SUM("Sep 24") AS "Sep 24",
      SUM("Oct 24") AS "Oct 24",
      SUM("Nov 24") AS "Nov 24",
      SUM("Dec 24") AS "Dec 24",
      SUM("Jan 25") AS "Jan 25",
      SUM("Feb 25") AS "Feb 25",
      SUM("Mar 25") AS "Mar 25",
      SUM("Apr 25") AS "Apr 25"
    FROM pivoted
    GROUP BY account_type
  ),
  combined AS (
    SELECT * FROM pivoted
    UNION ALL
    SELECT * FROM type_totals
  )
  SELECT *
  FROM combined
  ORDER BY
    account_type,
    CASE WHEN account_name LIKE 'Total %' THEN 1 ELSE 0 END,
    account_name; `,

  // Balance Sheet
  balanceSheetTableData: (realmId) => `
WITH months AS (

  SELECT * FROM (

    VALUES

      (2024, 4, 'Apr 24'), (2025, 3, 'Mar 25'), (2025, 4, 'Apr 25')

  ) AS m("year", "month", "label")

),

all_accounts AS (

  SELECT

    "accountId",

    "name" AS account_name,

    "type" AS account_type,

    "accountClassification"

  FROM "Account"

  WHERE "realmId" = '${realmId}'

    AND "accountClassification" IN ('Asset', 'Liability', 'Equity')

),

expanded_grid AS (

  SELECT

    aa."accountId",

    aa.account_name,

    aa.account_type,

    aa."accountClassification",

    m."year",

    m."month",

    m."label"

  FROM all_accounts aa

  CROSS JOIN months m

),

joined_data AS (

  SELECT

    eg."accountClassification",

    eg.account_type,

    eg.account_name,

    eg."label" AS month_label,

    COALESCE(SUM(bs."statementAmount"), 0) AS amount

  FROM expanded_grid eg

  LEFT JOIN "BalanceSheetReport" bs

    ON bs."accountId" = eg."accountId"

    AND bs."realmId" = '${realmId}'

    AND bs."year" = eg."year"

    AND bs."month" = eg."month"

  GROUP BY eg."accountClassification", eg.account_type, eg.account_name, eg."label"

),

pivoted AS (

  SELECT

    "accountClassification",

    account_type,

    account_name,

    SUM(CASE WHEN month_label = 'Apr 25' THEN amount ELSE 0 END) AS "Apr_25_Actuals",

    SUM(CASE WHEN month_label = 'Apr 24' THEN amount ELSE 0 END) AS "Apr_24_Prior_Year",

    SUM(CASE WHEN month_label = 'Mar 25' THEN amount ELSE 0 END) AS "Mar_25_Prior_Month"

  FROM joined_data

  GROUP BY "accountClassification", account_type, account_name

),

pivoted_with_variance AS (

  SELECT

    "accountClassification",

    account_type,

    account_name,

    "Apr_25_Actuals",

    "Apr_24_Prior_Year",

    ("Apr_25_Actuals" - "Apr_24_Prior_Year") AS "Variance_Prior_Year",

    "Mar_25_Prior_Month",

    ("Apr_25_Actuals" - "Mar_25_Prior_Month") AS "Variance_Prior_Month"

  FROM pivoted

),

account_type_totals AS (

  SELECT

    "accountClassification",

    account_type,

    'Total ' || account_type AS account_name,

    SUM("Apr_25_Actuals") AS "Apr_25_Actuals",

    SUM("Apr_24_Prior_Year") AS "Apr_24_Prior_Year",

    SUM("Variance_Prior_Year") AS "Variance_Prior_Year",

    SUM("Mar_25_Prior_Month") AS "Mar_25_Prior_Month",

    SUM("Variance_Prior_Month") AS "Variance_Prior_Month"

  FROM pivoted_with_variance

  GROUP BY "accountClassification", account_type

  -- Only include account type totals where account_type is different from accountClassification

  HAVING account_type != "accountClassification"

),

classification_totals AS (

  SELECT

    "accountClassification",

    'Total' AS account_type,

    'Total ' || "accountClassification" AS account_name,

    SUM("Apr_25_Actuals") AS "Apr_25_Actuals",

    SUM("Apr_24_Prior_Year") AS "Apr_24_Prior_Year",

    SUM("Variance_Prior_Year") AS "Variance_Prior_Year",

    SUM("Mar_25_Prior_Month") AS "Mar_25_Prior_Month",

    SUM("Variance_Prior_Month") AS "Variance_Prior_Month"

  FROM pivoted_with_variance

  GROUP BY "accountClassification"

),

combined AS (

  SELECT * FROM pivoted_with_variance

  UNION ALL

  SELECT * FROM account_type_totals

  UNION ALL

  SELECT * FROM classification_totals

)

SELECT 

  "accountClassification",

  account_type,

  account_name,

  "Apr_25_Actuals",

  "Apr_24_Prior_Year", 

  "Variance_Prior_Year",

  "Mar_25_Prior_Month",

  "Variance_Prior_Month"

FROM combined

ORDER BY

  CASE 

    WHEN "accountClassification" = 'Asset' THEN 1

    WHEN "accountClassification" = 'Liability' THEN 2

    WHEN "accountClassification" = 'Equity' THEN 3

    ELSE 4

  END,

  "accountClassification",

  account_type,

  CASE 
    WHEN account_name LIKE 'Total %' AND account_name NOT LIKE 'Total Asset%' AND account_name NOT LIKE 'Total Liability%' AND account_name NOT LIKE 'Total Equity%' THEN 1
    WHEN account_name LIKE 'Total Asset%' OR account_name LIKE 'Total Liability%' OR account_name LIKE 'Total Equity%' THEN 2
    ELSE 0 
  END,
  account_name
`,

  profitAndLossMonthsYTD: (realmId) =>
    `WITH all_accounts AS (

  SELECT

    "accountId",

    "name" AS account_name,

    "type" AS account_type,

    "accountClassification"

  FROM "Account"

  WHERE "realmId" = '${realmId}'

    AND "accountClassification" IN ('Revenue', 'Expense')

),

ytd_data AS (

  SELECT

    aa."accountId",

    aa.account_name,

    aa.account_type,

    aa."accountClassification",

    -- Year-to-date 2025 (Jan-Apr)

    COALESCE(SUM(CASE 

      WHEN pl."year" = 2025 AND pl."month" BETWEEN 1 AND 4 

      THEN pl."amount" 

      ELSE 0 

    END), 0) AS "Apr_25_YTD_Actuals",

    -- Year-to-date 2024 (Jan-Apr)

    COALESCE(SUM(CASE 

      WHEN pl."year" = 2024 AND pl."month" BETWEEN 1 AND 4 

      THEN pl."amount" 

      ELSE 0 

    END), 0) AS "Apr_24_YTD_Prior_Year"

  FROM all_accounts aa

  LEFT JOIN "ProfitLossReport" pl

    ON pl."accountId" = aa."accountId"

    AND pl."realmId" = '${realmId}'

    AND ((pl."year" = 2025 AND pl."month" BETWEEN 1 AND 4) 

      OR (pl."year" = 2024 AND pl."month" BETWEEN 1 AND 4))

  GROUP BY aa."accountId", aa.account_name, aa.account_type, aa."accountClassification"

),

total_income_calculation AS (

  SELECT

    SUM("Apr_25_YTD_Actuals") AS total_income_2025,

    SUM("Apr_24_YTD_Prior_Year") AS total_income_2024

  FROM ytd_data

  WHERE "accountClassification" = 'Revenue'

),

ytd_with_percentages AS (

  SELECT

    yd."accountClassification",

    yd.account_type,

    yd.account_name,

    yd."Apr_25_YTD_Actuals",

    yd."Apr_24_YTD_Prior_Year",

    (yd."Apr_25_YTD_Actuals" - yd."Apr_24_YTD_Prior_Year") AS "Variance_Amount",

    -- Calculate percentage of income for 2025

    CASE 

      WHEN tic.total_income_2025 != 0 THEN 

        ROUND((yd."Apr_25_YTD_Actuals" / tic.total_income_2025 * 100)::numeric, 2)

      ELSE 0 

    END AS "Apr_25_Percent_of_Income",

    -- Calculate percentage of income for 2024

    CASE 

      WHEN tic.total_income_2024 != 0 THEN 

        ROUND((yd."Apr_24_YTD_Prior_Year" / tic.total_income_2024 * 100)::numeric, 2)

      ELSE 0 

    END AS "Apr_24_Percent_of_Income",

    -- Calculate variance percentage

    CASE 

      WHEN yd."Apr_24_YTD_Prior_Year" != 0 THEN 

        ROUND(((yd."Apr_25_YTD_Actuals" - yd."Apr_24_YTD_Prior_Year") / ABS(yd."Apr_24_YTD_Prior_Year") * 100)::numeric, 2)

      ELSE 0 

    END AS "Variance_Percentage"

  FROM ytd_data yd

  CROSS JOIN total_income_calculation tic

),

account_type_totals AS (

  SELECT

    "accountClassification",

    account_type,

    'Total ' || account_type AS account_name,

    SUM("Apr_25_YTD_Actuals") AS "Apr_25_YTD_Actuals",

    SUM("Apr_24_YTD_Prior_Year") AS "Apr_24_YTD_Prior_Year",

    SUM("Variance_Amount") AS "Variance_Amount",

    -- Recalculate percentages for totals

    CASE 

      WHEN (SELECT total_income_2025 FROM total_income_calculation) != 0 THEN 

        ROUND((SUM("Apr_25_YTD_Actuals") / (SELECT total_income_2025 FROM total_income_calculation) * 100)::numeric, 2)

      ELSE 0 

    END AS "Apr_25_Percent_of_Income",

    CASE 

      WHEN (SELECT total_income_2024 FROM total_income_calculation) != 0 THEN 

        ROUND((SUM("Apr_24_YTD_Prior_Year") / (SELECT total_income_2024 FROM total_income_calculation) * 100)::numeric, 2)

      ELSE 0 

    END AS "Apr_24_Percent_of_Income",

    CASE 

      WHEN SUM("Apr_24_YTD_Prior_Year") != 0 THEN 

        ROUND((SUM("Variance_Amount") / ABS(SUM("Apr_24_YTD_Prior_Year")) * 100)::numeric, 2)

      ELSE 0 

    END AS "Variance_Percentage"

  FROM ytd_with_percentages

  GROUP BY "accountClassification", account_type

),

classification_totals AS (

  SELECT

    "accountClassification",

    'Total' AS account_type,

    'Total ' || "accountClassification" AS account_name,

    SUM("Apr_25_YTD_Actuals") AS "Apr_25_YTD_Actuals",

    SUM("Apr_24_YTD_Prior_Year") AS "Apr_24_YTD_Prior_Year",

    SUM("Variance_Amount") AS "Variance_Amount",

    -- Recalculate percentages for classification totals

    CASE 

      WHEN (SELECT total_income_2025 FROM total_income_calculation) != 0 THEN 

        ROUND((SUM("Apr_25_YTD_Actuals") / (SELECT total_income_2025 FROM total_income_calculation) * 100)::numeric, 2)

      ELSE 0 

    END AS "Apr_25_Percent_of_Income",

    CASE 

      WHEN (SELECT total_income_2024 FROM total_income_calculation) != 0 THEN 

        ROUND((SUM("Apr_24_YTD_Prior_Year") / (SELECT total_income_2024 FROM total_income_calculation) * 100)::numeric, 2)

      ELSE 0 

    END AS "Apr_24_Percent_of_Income",

    CASE 

      WHEN SUM("Apr_24_YTD_Prior_Year") != 0 THEN 

        ROUND((SUM("Variance_Amount") / ABS(SUM("Apr_24_YTD_Prior_Year")) * 100)::numeric, 2)

      ELSE 0 

    END AS "Variance_Percentage"

  FROM ytd_with_percentages

  GROUP BY "accountClassification"

),

combined AS (

  SELECT * FROM ytd_with_percentages

  UNION ALL

  SELECT * FROM account_type_totals

  UNION ALL

  SELECT * FROM classification_totals

)

SELECT 

  "accountClassification",

  account_type,

  account_name,

  "Apr_25_YTD_Actuals",

  "Apr_25_Percent_of_Income",

  "Apr_24_YTD_Prior_Year", 

  "Apr_24_Percent_of_Income",

  "Variance_Amount",

  "Variance_Percentage"

FROM combined

ORDER BY

  CASE 

    WHEN "accountClassification" = 'Revenue' THEN 1

    WHEN "accountClassification" = 'Expense' THEN 2

    ELSE 3

  END,

  "accountClassification",

  account_type,

  CASE 

    WHEN account_name LIKE 'Total %' AND account_name NOT LIKE 'Total Revenue%' AND account_name NOT LIKE 'Total Expense%' THEN 1

    WHEN account_name LIKE 'Total Revenue%' OR account_name LIKE 'Total Expense%' THEN 2

    ELSE 0 

  END,

  account_name;
 `,

  profitAndLossMonthly: (realmId) => ` WITH months AS (
  SELECT * FROM (
    VALUES
      (2024, 4, 'Apr_24'), (2025, 4, 'Apr_25')
  ) AS m("year", "month", "label")
),
all_accounts AS (
  SELECT
    "accountId",
    "name" AS account_name,
    "type" AS account_type
  FROM "Account"
  WHERE "realmId" = '${realmId}'
),
expanded_grid AS (
  SELECT
    aa."accountId",
    aa.account_name,
    aa.account_type,
    m."year",
    m."month",
    m."label"
  FROM all_accounts aa
  CROSS JOIN months m
),
joined_data AS (
  SELECT
    eg.account_type,
    eg.account_name,
    eg."label" AS month_label,
    COALESCE(SUM(pl."amount"), 0) AS amount
  FROM expanded_grid eg
  LEFT JOIN "ProfitLossReport" pl
    ON pl."accountId" = eg."accountId"
    AND pl."realmId" = '${realmId}'
    AND pl."year" = eg."year"
    AND pl."month" = eg."month"
  GROUP BY eg.account_type, eg.account_name, eg."label"
),
pivoted AS (
  SELECT
    account_type,
    account_name,
    MAX(CASE WHEN month_label = 'Apr_25' THEN amount ELSE 0 END) AS "Apr_25_Actuals",
    MAX(CASE WHEN month_label = 'Apr_24' THEN amount ELSE 0 END) AS "Apr_24_Prior_Year"
  FROM joined_data
  GROUP BY account_type, account_name
),
total_revenue_calc AS (
  SELECT
    SUM("Apr_25_Actuals") AS total_revenue_2025,
    SUM("Apr_24_Prior_Year") AS total_revenue_2024
  FROM pivoted
  WHERE account_type LIKE '%Income%' 
     OR account_type LIKE '%Revenue%'
     OR account_type LIKE '%Sales%'
),
pivoted_with_calculations AS (
  SELECT
    p.account_type,
    p.account_name,
    p."Apr_25_Actuals",
    -- Calculate % of Income for Apr 25
    CASE 
      WHEN trc.total_revenue_2025 != 0 THEN 
        ROUND((p."Apr_25_Actuals" / trc.total_revenue_2025 * 100)::numeric, 0)
      ELSE 0 
    END AS "Apr_25_Percent_of_Income",
    p."Apr_24_Prior_Year",
    -- Calculate % of Income for Apr 24
    CASE 
      WHEN trc.total_revenue_2024 != 0 THEN 
        ROUND((p."Apr_24_Prior_Year" / trc.total_revenue_2024 * 100)::numeric, 0)
      ELSE 0 
    END AS "Apr_24_Percent_of_Income",
    -- Calculate Variance Amount
    (p."Apr_25_Actuals" - p."Apr_24_Prior_Year") AS "Variance_Amount",
    -- Calculate Variance Percentage
    CASE 
      WHEN p."Apr_24_Prior_Year" != 0 THEN 
        ROUND(((p."Apr_25_Actuals" - p."Apr_24_Prior_Year") / ABS(p."Apr_24_Prior_Year") * 100)::numeric, 0)
      ELSE 
        CASE WHEN p."Apr_25_Actuals" != 0 THEN 100 ELSE 0 END
    END AS "Variance_Percentage"
  FROM pivoted p
  CROSS JOIN total_revenue_calc trc
),
type_totals AS (
  SELECT
    account_type,
    'Total ' || account_type AS account_name,
    SUM("Apr_25_Actuals") AS "Apr_25_Actuals",
    -- Calculate % of Income for totals
    CASE 
      WHEN (SELECT total_revenue_2025 FROM total_revenue_calc) != 0 THEN 
        ROUND((SUM("Apr_25_Actuals") / (SELECT total_revenue_2025 FROM total_revenue_calc) * 100)::numeric, 0)
      ELSE 0 
    END AS "Apr_25_Percent_of_Income",
    SUM("Apr_24_Prior_Year") AS "Apr_24_Prior_Year",
    CASE 
      WHEN (SELECT total_revenue_2024 FROM total_revenue_calc) != 0 THEN 
        ROUND((SUM("Apr_24_Prior_Year") / (SELECT total_revenue_2024 FROM total_revenue_calc) * 100)::numeric, 0)
      ELSE 0 
    END AS "Apr_24_Percent_of_Income",
    SUM("Variance_Amount") AS "Variance_Amount",
    CASE 
      WHEN SUM("Apr_24_Prior_Year") != 0 THEN 
        ROUND((SUM("Variance_Amount") / ABS(SUM("Apr_24_Prior_Year")) * 100)::numeric, 0)
      ELSE 
        CASE WHEN SUM("Apr_25_Actuals") != 0 THEN 100 ELSE 0 END
    END AS "Variance_Percentage"
  FROM pivoted_with_calculations
  GROUP BY account_type
),
classification_totals AS (
  SELECT
    'Total' AS account_type,
    'Total Income' AS account_name,
    SUM("Apr_25_Actuals") AS "Apr_25_Actuals",
    CASE 
      WHEN (SELECT total_revenue_2025 FROM total_revenue_calc) != 0 THEN 
        ROUND((SUM("Apr_25_Actuals") / (SELECT total_revenue_2025 FROM total_revenue_calc) * 100)::numeric, 0)
      ELSE 0 
    END AS "Apr_25_Percent_of_Income",
    SUM("Apr_24_Prior_Year") AS "Apr_24_Prior_Year",
    CASE 
      WHEN (SELECT total_revenue_2024 FROM total_revenue_calc) != 0 THEN 
        ROUND((SUM("Apr_24_Prior_Year") / (SELECT total_revenue_2024 FROM total_revenue_calc) * 100)::numeric, 0)
      ELSE 0 
    END AS "Apr_24_Percent_of_Income",
    SUM("Variance_Amount") AS "Variance_Amount",
    CASE 
      WHEN SUM("Apr_24_Prior_Year") != 0 THEN 
        ROUND((SUM("Variance_Amount") / ABS(SUM("Apr_24_Prior_Year")) * 100)::numeric, 0)
      ELSE 
        CASE WHEN SUM("Apr_25_Actuals") != 0 THEN 100 ELSE 0 END
    END AS "Variance_Percentage"
  FROM pivoted_with_calculations
  WHERE account_type LIKE '%Income%' 
     OR account_type LIKE '%Revenue%'
     OR account_type LIKE '%Sales%'
),
combined AS (
  SELECT * FROM pivoted_with_calculations
  UNION ALL
  SELECT * FROM type_totals
  UNION ALL
  SELECT * FROM classification_totals
)
SELECT 
  account_type,
  account_name,
  "Apr_25_Actuals",
  "Apr_25_Percent_of_Income",
  "Apr_24_Prior_Year",
  "Apr_24_Percent_of_Income",
  "Variance_Amount",
  "Variance_Percentage"
FROM combined
ORDER BY
  account_type,
  CASE WHEN account_name LIKE 'Total %' THEN 1 ELSE 0 END,
  account_name;`,

  
  wagesRevenueMonthWise: (realmId) => `
WITH date_range AS (
  SELECT
    EXTRACT(YEAR FROM d)::INT AS year,
    EXTRACT(MONTH FROM d)::INT AS month
  FROM generate_series(
    DATE '2024-04-01',
    DATE '2025-03-01',
    INTERVAL '1 month'
  ) AS d
),
wage_accounts AS (
  SELECT "accountId", "userId", "realmId"
  FROM "Account"
  WHERE 
    "accountSubTypeName" ILIKE '%wage%' OR
    "name" ILIKE '%salary%' OR 
    "name" ILIKE '%wages%' OR 
    "name" ILIKE '%payroll%'
),
income_vs_wages AS (
  SELECT 
    dr.year,
    dr.month,
    COALESCE(SUM(CASE WHEN a."accountId" IN (
      SELECT "accountId" FROM wage_accounts
    ) THEN pl."amount" ELSE 0 END), 0) AS wages,
    COALESCE(SUM(CASE WHEN a."accountId" NOT IN (
      SELECT "accountId" FROM wage_accounts
    ) THEN pl."amount" ELSE 0 END), 0) AS income
  FROM date_range dr
  LEFT JOIN "ProfitLossReport" pl 
    ON pl."year" = dr.year AND pl."month" = dr.month AND pl."realmId" = '${realmId}'
  LEFT JOIN "Account" a 
    ON a."accountId" = pl."accountId" AND a."userId" = pl."userId" AND a."realmId" = pl."realmId"
  GROUP BY dr.year, dr.month
  ORDER BY dr.year, dr.month
)
SELECT * FROM income_vs_wages;

`,
};

// Helper function to execute a single query with realmId parameter
export async function executeQuery(queryFunc, realmId, queryName) {
  try {
    const query = queryFunc(realmId);
    const result = await prisma.$queryRawUnsafe(query); // Use the generated query string here
    // const convertData = flattenFinancialData(result);
    return {
      success: true,
      message: 'Data get successfully',
      data: result,
    };
  } catch (error) {
    console.error(`Error executing ${queryName}:`, error);
    return {
      success: false,
      error: error.message,
      data: [],
    };
  }
}
